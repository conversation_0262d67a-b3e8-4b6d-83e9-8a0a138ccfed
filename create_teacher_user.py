"""
<PERSON><PERSON><PERSON> to create a teacher user directly in the user_db database.

This script creates a teacher user with a main_code for testing purposes.
"""

import os
import bcrypt
from datetime import datetime
import psycopg2
from dotenv import load_dotenv
import random
import string

# Load environment variables
load_dotenv()

def get_db_connection():
    """Get a database connection to user_db."""
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'postgres')
    db_host = os.environ.get('DB_HOST', 'localhost')
    db_port = os.environ.get('DB_PORT', '5432')
    
    conn = psycopg2.connect(
        dbname='user_db',
        user=db_user,
        password=db_password,
        host=db_host,
        port=db_port
    )
    
    return conn

def hash_password(password):
    """Hash a password using bcrypt."""
    if isinstance(password, str):
        password = password.encode('utf-8')
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password, salt)
    return hashed.decode('utf-8')

def generate_main_code():
    """Generate a unique main code."""
    letters = ''.join(random.choices(string.ascii_uppercase, k=3))
    numbers = ''.join(random.choices(string.digits, k=3))
    return f"{letters}{numbers}"

def create_teacher_user():
    """Create a teacher user with main_code."""
    username = "teacher1"
    password = "password123"
    email = "<EMAIL>"
    role = "Teacher"
    main_code = generate_main_code()
    
    # Hash the password
    hashed_password = hash_password(password)
    
    # Current timestamp
    now = datetime.now()
    
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # Check if user already exists
        cursor.execute(
            "SELECT id FROM users WHERE username = %s OR email = %s",
            (username, email)
        )
        
        if cursor.fetchone():
            print(f"User with username '{username}' or email '{email}' already exists.")
            return False
        
        # Insert user
        cursor.execute(
            """
            INSERT INTO users (username, password, email, role, is_admin, main_code, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING id
            """,
            (username, hashed_password, email, role, False, main_code, now, now)
        )
        
        user_id = cursor.fetchone()[0]
        conn.commit()
        print(f"Teacher user '{username}' created successfully with ID {user_id} and main_code '{main_code}'!")
        print(f"Login credentials: username='{username}', password='{password}'")
        
        return True
        
    except Exception as e:
        print(f"Error creating teacher user: {e}")
        if 'conn' in locals():
            conn.rollback()
        return False
    finally:
        if 'conn' in locals():
            cursor.close()
            conn.close()

if __name__ == '__main__':
    create_teacher_user()
