"""
Check if a token can be decoded with the JWT_SECRET.

This script checks if a token can be decoded with the JWT_SECRET from the .env file.
"""

import os
import jwt
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get JWT secret from environment variables
JWT_SECRET = os.environ.get('JWT_SECRET', 'simple-jwt-secret-for-school-management-system')

# Token to check
TOKEN = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOjMsInVzZXJuYW1lIjoic3VwZXJhZG1pbiIsInJvbGUiOiJTdXBlciBBZG1pbiIsImlzX2FkbWluIjp0cnVlLCJleHAiOjE3NDcxMzEzNTF9.mL9dpdJUVCZClv4tx4Bv33OgQ4gKOAEQ8hYDoqthj6M"

print(f"JWT_SECRET: {JWT_SECRET}")
print(f"Token: {TOKEN}")

try:
    # Decode and verify token
    payload = jwt.decode(TOKEN, JWT_SECRET, algorithms=['HS256'])
    print(f"Token payload: {payload}")
    print("Token verification successful!")
except Exception as e:
    print(f"Token verification failed: {str(e)}")
