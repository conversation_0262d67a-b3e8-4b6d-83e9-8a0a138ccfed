"""
Controllers for the Auth Service.

This module contains the business logic for authentication.

English: This file contains the logic for user login and token generation
Tanglish: Indha file-la user login and token generation-kku logic irukku
"""

import os
import jwt
import requests
from datetime import datetime, timedelta
from flask import jsonify, request, current_app
from auth_service.common.utils import handle_error, call_service
from auth_service.models import User
from auth_service.common.db_config import db
from auth_service.config import get_config
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get JWT secret from environment variables
JWT_SECRET = os.environ.get('JWT_SECRET', 'simple-jwt-secret-for-school-management-system')

def login():
    """
    Authenticate a user and generate a JWT token.

    Returns:
        JSON response with token and user information

    English: This function checks username and password, then generates a JWT token
    Tanglish: Indha function username, password-a check panni, JWT token-a generate pannum
    """
    # Get request data
    data = request.get_json()
    if not data:
        return handle_error("No data provided", 400)

    # Check if username and password are provided
    username = data.get('username')
    password = data.get('password')
    if not username or not password:
        return handle_error("Username and password are required", 400)

    print(f"Login attempt for username: {username}")

    # Find the user
    user = User.query.filter_by(username=username).first()
    if not user:
        print(f"User not found: {username}")
        return handle_error("Invalid username or password", 401)

    print(f"User found: {user.username}, ID: {user.id}, Role: {user.role}")

    # Check password
    password_match = user.check_password(password)
    print(f"Password match: {password_match}")

    if not password_match:
        print(f"Password verification failed for user: {username}")
        return handle_error("Invalid username or password", 401)

    print(f"Password verified successfully for user: {username}")

    # Generate JWT token
    token = generate_token(user)

    # Prepare basic user information
    user_info = {
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "role": user.role,
        "is_admin": user.is_admin
    }

    # For Teacher, Admin, and Super Admin roles, fetch complete user data from user service
    if user.role in ['Teacher', 'Admin', 'Super Admin']:
        try:
            # Get configuration
            config = get_config()
            user_service_url = f"{config.USER_SERVICE_URL}/api/users/users/{user.id}"

            print(f"Fetching complete user data from: {user_service_url}")

            # Create JWT token for internal service call
            internal_token = generate_token(user)
            headers = {
                'Authorization': f'Bearer {internal_token}',
                'Content-Type': 'application/json'
            }

            # Call user service to get complete user data
            response = call_service(user_service_url, method='GET', headers=headers)

            if response and response.status_code == 200:
                user_service_data = response.json()
                if 'user' in user_service_data:
                    complete_user_data = user_service_data['user']
                    # Update user_info with complete data including main_code
                    user_info.update(complete_user_data)
                    print(f"Successfully fetched complete user data with main_code: {complete_user_data.get('main_code')}")
                else:
                    print("User service response doesn't contain user data")
            else:
                print(f"Failed to fetch user data from user service. Status: {response.status_code if response else 'No response'}")

        except Exception as e:
            print(f"Error fetching user data from user service: {str(e)}")
            # Continue with basic user info if service call fails

    # Return token and user information
    return jsonify({
        "token": token,
        "user": user_info
    })

def generate_token(user):
    """
    Generate a JWT token for a user.

    Args:
        user: User object

    Returns:
        JWT token string

    English: This function creates a JWT token with user information
    Tanglish: Indha function user information-oda JWT token-a create pannum
    """
    # Set token expiration time (e.g., 24 hours)
    expiration = datetime.utcnow() + timedelta(hours=24)

    # Create token payload
    payload = {
        'sub': str(user.id),  # Convert user ID to string
        'username': user.username,
        'role': user.role,
        'is_admin': user.is_admin,
        'exp': expiration
    }

    # Generate token
    token = jwt.encode(payload, JWT_SECRET, algorithm='HS256')

    return token

def verify_token():
    """
    Verify a JWT token.

    Returns:
        JSON response with token validity and user information

    English: This function checks if a token is valid
    Tanglish: Indha function token valid-a irukka nu check pannum
    """
    # Get token from Authorization header
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return handle_error("Missing or invalid token", 401)

    token = auth_header.split(' ')[1]

    try:
        # Decode and verify token
        payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])

        # Get user from database
        user_id = payload.get('sub')
        user = User.query.get(user_id)
        if not user:
            return handle_error("User not found", 404)

        # Return user information
        return jsonify({
            "valid": True,
            "user": {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "role": user.role,
                "is_admin": user.is_admin
            }
        })

    except jwt.ExpiredSignatureError:
        return handle_error("Token expired", 401)

    except jwt.InvalidTokenError:
        return handle_error("Invalid token", 401)
