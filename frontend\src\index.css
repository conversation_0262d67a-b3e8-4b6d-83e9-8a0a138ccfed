@import "tailwindcss";

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
}

/* Custom utility classes */
@layer components {
  .btn {
    @apply px-4 py-2 rounded font-medium transition-colors;
  }

  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700;
  }

  .btn-secondary {
    @apply bg-gray-200 text-gray-800 hover:bg-gray-300;
  }

  .card {
    @apply bg-white rounded-lg shadow p-6 mb-4;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  .form-group {
    @apply mb-4;
  }

  .alert {
    @apply p-4 mb-4 rounded;
  }

  .alert-danger {
    @apply bg-red-100 text-red-700 border border-red-200;
  }

  .alert-success {
    @apply bg-green-100 text-green-700 border border-green-200;
  }

  /* Sidebar text color override */
  .sidebar-text-white {
    color: white !important;
  }

  .sidebar-text-white * {
    color: white !important;
  }
}
