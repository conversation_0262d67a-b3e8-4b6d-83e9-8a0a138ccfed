"""
<PERSON><PERSON>t to register a Super Admin user.

This script registers a Super Admin user directly in the auth_db and user_db databases.
It should be run once during initial setup.

Usage:
    python register_super_admin.py --username superadmin --password yourpassword --email <EMAIL>
"""

import os
import sys
import argparse
import bcrypt
from datetime import datetime
import psycopg2
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def get_db_connection(db_name):
    """Get a database connection."""
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'postgres')
    db_host = os.environ.get('DB_HOST', 'localhost')
    db_port = os.environ.get('DB_PORT', '5432')
    
    conn = psycopg2.connect(
        dbname=db_name,
        user=db_user,
        password=db_password,
        host=db_host,
        port=db_port
    )
    
    return conn

def hash_password(password):
    """Hash a password using bcrypt."""
    # Convert password to bytes if it's a string
    if isinstance(password, str):
        password = password.encode('utf-8')

    # Generate a salt and hash the password
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password, salt)

    # Return the hashed password as a string
    return hashed.decode('utf-8')

def register_super_admin(username, password, email):
    """Register a Super Admin user in both auth_db and user_db."""
    # Hash the password
    hashed_password = hash_password(password)
    
    # Current timestamp
    now = datetime.utcnow()
    
    # Register in auth_db
    try:
        conn_auth = get_db_connection('auth_db')
        cursor_auth = conn_auth.cursor()
        
        # Check if user already exists
        cursor_auth.execute(
            "SELECT id FROM users WHERE username = %s OR email = %s",
            (username, email)
        )
        
        if cursor_auth.fetchone():
            print(f"Error: User with username '{username}' or email '{email}' already exists in auth_db.")
            return False
        
        # Insert user
        cursor_auth.execute(
            """
            INSERT INTO users (username, password, email, role, is_admin, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            RETURNING id
            """,
            (username, hashed_password, email, 'Super Admin', True, now, now)
        )
        
        user_id = cursor_auth.fetchone()[0]
        conn_auth.commit()
        print(f"Super Admin user '{username}' created successfully in auth_db with ID {user_id}!")
        
    except Exception as e:
        print(f"Error creating Super Admin user in auth_db: {e}")
        if 'conn_auth' in locals():
            conn_auth.rollback()
        return False
    finally:
        if 'conn_auth' in locals():
            cursor_auth.close()
            conn_auth.close()
    
    # Register in user_db
    try:
        conn_user = get_db_connection('user_db')
        cursor_user = conn_user.cursor()
        
        # Check if user already exists
        cursor_user.execute(
            "SELECT id FROM users WHERE username = %s OR email = %s",
            (username, email)
        )
        
        if cursor_user.fetchone():
            print(f"Error: User with username '{username}' or email '{email}' already exists in user_db.")
            return False
        
        # Insert user
        cursor_user.execute(
            """
            INSERT INTO users (username, password, email, role, is_admin, created_at, updated_at)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            RETURNING id
            """,
            (username, hashed_password, email, 'Super Admin', True, now, now)
        )
        
        user_id = cursor_user.fetchone()[0]
        conn_user.commit()
        print(f"Super Admin user '{username}' created successfully in user_db with ID {user_id}!")
        
        return True
        
    except Exception as e:
        print(f"Error creating Super Admin user in user_db: {e}")
        if 'conn_user' in locals():
            conn_user.rollback()
        return False
    finally:
        if 'conn_user' in locals():
            cursor_user.close()
            conn_user.close()

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Register a Super Admin user.')
    parser.add_argument('--username', required=True, help='Super Admin username')
    parser.add_argument('--password', required=True, help='Super Admin password')
    parser.add_argument('--email', required=True, help='Super Admin email')
    
    args = parser.parse_args()
    
    # Register Super Admin user
    success = register_super_admin(args.username, args.password, args.email)
    
    # Exit with appropriate status code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
