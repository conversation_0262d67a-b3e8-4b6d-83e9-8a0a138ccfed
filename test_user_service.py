"""
<PERSON><PERSON><PERSON> to test the user service.

This script tests the user service by getting all users.
"""

import requests
import jwt
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get JWT secret from environment variables
JWT_SECRET = os.environ.get('JWT_SECRET', 'simple-jwt-secret-for-school-management-system')

def generate_token(user_id, username, role, is_admin=False):
    """
    Generate a JWT token.
    
    Args:
        user_id: User ID
        username: Username
        role: User role
        is_admin: Whether the user is an admin
        
    Returns:
        JWT token string
    """
    # Set token expiration time (e.g., 24 hours)
    expiration = datetime.utcnow() + timedelta(hours=24)
    
    # Create token payload
    payload = {
        'sub': str(user_id),  # Convert user ID to string
        'username': username,
        'role': role,
        'is_admin': is_admin,
        'exp': expiration
    }
    
    # Generate token
    token = jwt.encode(payload, JWT_SECRET, algorithm='HS256')
    
    return token

def get_users(token):
    """
    Get all users.
    
    Args:
        token: JWT token string
        
    Returns:
        Response from the endpoint
    """
    url = 'http://localhost:5001/api/users/users'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    print(f"Sending request to {url}")
    print(f"Headers: {headers}")
    
    response = requests.get(url, headers=headers)
    
    print(f"Response status code: {response.status_code}")
    print(f"Response content: {response.text}")
    
    return response

if __name__ == '__main__':
    # Generate a token for a Super Admin
    token = generate_token(9, 'superadmin', 'Super Admin', True)
    print(f"Generated token: {token}")
    
    # Get all users
    response = get_users(token)
