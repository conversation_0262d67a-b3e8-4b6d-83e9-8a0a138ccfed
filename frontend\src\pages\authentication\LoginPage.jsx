// /**
//  * Login Page
//  *
//  * This page handles user authentication for Pandora 2.
//  */

// import { useState } from 'react';
// import { useNavigate, Link } from 'react-router-dom';
// import { useAuth } from '@/contexts/AuthContext';
// import Input from '@/components/field/Input';
// import Button from '@/components/field/Button';
// import Card from '@/components/common/Card';
// import config from '@/config';
// import PandoraImage from '@/assets/pandora.png'; // Importing the graphic

// const LoginPage = () => {
//   const [username, setUsername] = useState('');
//   const [password, setPassword] = useState('');
//   const [error, setError] = useState('');
//   const [loading, setLoading] = useState(false);

//   const { login } = useAuth();
//   const navigate = useNavigate();

//   const handleSubmit = async (e) => {
//     e.preventDefault();

//     // Validate form
//     if (!username || !password) {
//       setError('Username and password are required');
//       return;
//     }

//     try {
//       setLoading(true);
//       setError('');

//       // Call login function from auth context
//       await login(username, password);

//       // Redirect to dashboard
//       navigate('/dashboard');
//     } catch (error) {
//       setError(error.error || 'Login failed. Please check your credentials.');
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative">
//       {/* Decorative stars */}
//       <div className="absolute right-10 top-20">
//         <svg className="w-12 h-12 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
//           <path d="M12 2l2.4 7.2h7.6l-6 4.8 2.4 7.2-6-4.8-6 4.8 2.4-7.2-6-4.8h7.6z" />
//         </svg>
//       </div>
//       <div className="absolute right-20 top-10">
//         <svg className="w-8 h-8 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
//           <path d="M12 2l2.4 7.2h7.6l-6 4.8 2.4 7.2-6-4.8-6 4.8 2.4-7.2-6-4.8h7.6z" />
//         </svg>
//       </div>

//       <div className="flex flex-col md:flex-row items-center max-w-4xl w-full space-y-8 md:space-y-0 md:space-x-8 relative z-10">
//         {/* Login Form */}
//         <div className="w-full md:w-1/2">
//           <div className="text-center">
//             <h1 className="text-4xl font-extrabold text-white tracking-wider">PANDORA 2</h1>
//             <p className="mt-2 text-sm text-gray-300">Sign in to your account!</p>
//           </div>

//           <Card className="bg-gray-200 shadow-lg rounded-xl p-8 mt-6">
//             <form className="space-y-6" onSubmit={handleSubmit}>
//               {error && (
//                 <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4 rounded">
//                   <div className="flex">
//                     <div className="flex-shrink-0">
//                       <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
//                         <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
//                       </svg>
//                     </div>
//                     <div className="ml-3">
//                       <p className="text-sm text-red-700">{error}</p>
//                     </div>
//                   </div>
//                 </div>
//               )}

//               <Input
//                 id="username"
//                 name="username"
//                 type="text"
//                 label="Username"
//                 value={username}
//                 onChange={(e) => setUsername(e.target.value)}
//                 required
//                 autoComplete="username"
//                 className="bg-white rounded-lg"
//               />

//               <Input
//                 id="password"
//                 name="password"
//                 type="password"
//                 label="Password"
//                 value={password}
//                 onChange={(e) => setPassword(e.target.value)}
//                 required
//                 autoComplete="current-password"
//                 className="bg-white rounded-lg"
//               />

//               <div>
//                 <Button
//                   type="submit"
//                   disabled={loading}
//                   loading={loading}
//                   fullWidth={true}
//                   variant="primary"
//                   className="bg-white text-gray-800 hover:bg-gray-100 rounded-lg"
//                 >
//                   Sign In
//                 </Button>
//               </div>

//               <div className="text-center">
//                 <Link
//                   to="/forget-password"
//                   className="text-blue-600 hover:text-blue-500 text-sm font-medium transition-colors duration-300"
//                 >
//                   Forget your password?
//                 </Link>
//               </div>
//             </form>
//           </Card>
//         </div>

//         {/* Graphic from pandora.png */}
//         <div className="w-full md:w-1/2 flex justify-center">
//           <img src={PandoraImage} alt="Pandora 2 Graphic" className="max-w-full h-auto" />
//         </div>
//       </div>
//     </div>
//   );
// };

// export default LoginPage;






// /**
//  * Login Page
//  *
//  * This page handles user authentication for Pandora 2.
//  */

// import { useState } from 'react';
// import { useNavigate, Link } from 'react-router-dom';
// import { useAuth } from '@/contexts/AuthContext';
// import Input from '@/components/field/Input';
// import Button from '@/components/field/Button';
// import Card from '@/components/common/Card';
// import config from '@/config';
// import PandoraImage from '@/assets/pandora.png'; // Importing the graphic

// const LoginPage = () => {
//   const [username, setUsername] = useState(''); // Pre-filled username
//   const [password, setPassword] = useState(''); // Pre-filled masked password
//   const [error, setError] = useState('');
//   const [loading, setLoading] = useState(false);

//   const { login } = useAuth();
//   const navigate = useNavigate();

//   const handleSubmit = async (e) => {
//     e.preventDefault();

//     // Validate form
//     if (!username || !password) {
//       setError('Username and password are required');
//       return;
//     }

//     try {
//       setLoading(true);
//       setError('');

//       // Call login function from auth context
//       await login(username, password);

//       // Redirect to dashboard
//       navigate('/dashboard');
//     } catch (error) {
//       setError(error.error || 'Login failed. Please check your credentials.');
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <div className="min-h-screen bg-gray-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative">
//       {/* Decorative stars */}


//       <div className="flex flex-col md:flex-row items-center max-w-4xl w-full space-y-8 md:space-y-0 md:space-x-8 relative z-10">
//         {/* Login Form */}
//         <div className="w-full md:w-1/2">
//           <div className="text-center">
//             <h1 className="text-4xl font-extrabold text-gray-900 tracking-wider">PANDORA 2</h1>
//             <p className="mt-2 text-sm text-gray-600">Sign in to your account!</p>
//           </div>

//           <Card className="bg-gray-100 shadow-lg rounded-xl p-8 mt-6">
//             <form className="space-y-6" onSubmit={handleSubmit}>
//               {error && (
//                 <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4 rounded">
//                   <div className="flex">
//                     <div className="flex-shrink-0">
//                       <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
//                         <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
//                       </svg>
//                     </div>
//                     <div className="ml-3">
//                       <p className="text-sm text-red-700">{error}</p>
//                     </div>
//                   </div>
//                 </div>
//               )}

//               <Input
//                 id="username"
//                 name="username"
//                 type="text"
//                 label="Username*"
//                 value={username}
//                 onChange={(e) => setUsername(e.target.value)}
//                 required
//                 autoComplete="username"
//                 className="bg-white rounded-lg"
//               />

//               <Input
//                 id="password"
//                 name="password"
//                 type="password"
//                 label="Password*"
//                 value={password}
//                 onChange={(e) => setPassword(e.target.value)}
//                 required
//                 autoComplete="current-password"
//                 className="bg-white rounded-lg"
//               />

//               <div>
//                 <Button
//                   type="submit"
//                   disabled={loading}
//                   loading={loading}
//                   fullWidth={true}
//                   variant="primary"
//                   className="bg- text-gray-800 hover:bg-gray-100 rounded-lg"
//                 >
//                   Sign In
//                 </Button>
//               </div>

//               <div className="text-center">
//                 <Link
//                   to="/forget-password"
//                   className="text-blue-600 hover:text-blue-500 text-sm font-medium transition-colors duration-300"
//                 >
//                   Forget your password?
//                 </Link>
//               </div>
//             </form>
//           </Card>
//         </div>

//         {/* Graphic from pandora.png */}
//         <div className="w-full md:w-1/2 flex justify-center">
//           <img src={PandoraImage} alt="Pandora 2 Graphic" className="max-w-1050 h-150" />
//         </div>
//       </div>
//     </div>
//   );
// };

// export default LoginPage;




// import { useState } from 'react';
// import { useNavigate, Link } from 'react-router-dom';
// import { useAuth } from '@/contexts/AuthContext';
// import Input from '@/components/field/Input';
// import Button from '@/components/field/Button';
// import Card from '@/components/common/Card';
// import config from '@/config';
// import PandoraImage from '@/assets/pandora.png'; // Importing the graphic

// const LoginPage = () => {
//   const [username, setUsername] = useState(''); // Pre-filled username
//   const [password, setPassword] = useState(''); // Pre-filled masked password
//   const [error, setError] = useState('');
//   const [loading, setLoading] = useState(false);

//   const { login } = useAuth();
//   const navigate = useNavigate();

//   const handleSubmit = async (e) => {
//     e.preventDefault();

//     if (!username || !password) {
//       setError('Username and password are required');
//       return;
//     }

//     try {
//       setLoading(true);
//       setError('');

//       await login(username, password);
//       navigate('/dashboard');
//     } catch (error) {
//       setError(error.error || 'Login failed. Please check your credentials.');
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      
//       {/* Decorative background stars */}
//       <div className="absolute top-10 right-10 animate-pulse">
//         <svg className="w-12 h-12 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
//           <path d="M12 2l2.4 7.2h7.6l-6 4.8 2.4 7.2-6-4.8-6 4.8 2.4-7.2-6-4.8h7.6z" />
//         </svg>
//       </div>

//       <div className="absolute top-24 right-32 animate-pulse delay-100">
//         <svg className="w-8 h-8 text-yellow-300" fill="currentColor" viewBox="0 0 24 24">
//           <path d="M12 2l2.4 7.2h7.6l-6 4.8 2.4 7.2-6-4.8-6 4.8 2.4-7.2-6-4.8h7.6z" />
//         </svg>
//       </div>

//       {/* Main Content */}
//       <div className="flex flex-col md:flex-row items-center max-w-5xl w-full space-y-10 md:space-y-0 md:space-x-12 bg-white shadow-2xl rounded-3xl p-8 md:p-12">
        
//         {/* Left Side - Form */}
//         <div className="w-full md:w-1/2">
//           <div className="text-center mb-8">
//             <h1 className="text-5xl font-extrabold text-gray-900 tracking-wide">PANDORA 2</h1>
//             <p className="mt-3 text-base text-gray-600">Sign in to access your dashboard</p>
//           </div>

//           <Card className="bg-gray-50 shadow-lg rounded-2xl p-8 transition-all duration-300">
//             <form onSubmit={handleSubmit} className="space-y-6">
//               {error && (
//                 <div className="bg-red-50 border-l-4 border-red-500 p-4 rounded-lg mb-4">
//                   <div className="flex">
//                     <svg className="h-6 w-6 text-red-500 mt-0.5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
//                       <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
//                     </svg>
//                     <div className="ml-3">
//                       <p className="text-sm text-red-700">{error}</p>
//                     </div>
//                   </div>
//                 </div>
//               )}

//               <Input
//                 id="username"
//                 name="username"
//                 type="text"
//                 label="Username*"
//                 value={username}
//                 onChange={(e) => setUsername(e.target.value)}
//                 required
//                 autoComplete="username"
//                 className="bg-white border-gray-300 rounded-xl focus:border-blue-500 focus:ring focus:ring-blue-200"
//               />

//               <Input
//                 id="password"
//                 name="password"
//                 type="password"
//                 label="Password*"
//                 value={password}
//                 onChange={(e) => setPassword(e.target.value)}
//                 required
//                 autoComplete="current-password"
//                 className="bg-white border-gray-300 rounded-xl focus:border-blue-500 focus:ring focus:ring-blue-200"
//               />

//               <Button
//                 type="submit"
//                 disabled={loading}
//                 loading={loading}
//                 fullWidth={true}
//                 variant="primary"
//                 className="bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-xl py-3 transition-colors duration-300"
//               >
//                 Sign In
//               </Button>

//               <div className="text-center mt-4">
//                 <Link
//                   to="/forget-password"
//                   className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors duration-300"
//                 >
//                   Forgot your password?
//                 </Link>
//               </div>
//             </form>
//           </Card>
//         </div>

//         {/* Right Side - Image */}
//         <div className="w-full md:w-1/2 flex justify-center">
//           <img
//             src={PandoraImage}
//             alt="Pandora 2 Graphic"
//             className="max-w-full h-auto object-contain rounded-2xl shadow-md"
//           />
//         </div>
//       </div>
//     </div>
//   );
// };

// export default LoginPage;






// /**
//  * Login Page
//  *
//  * This page handles user authentication for Pandora 2.
//  */

// import { useState } from 'react';
// import { useNavigate, Link } from 'react-router-dom';
// import { useAuth } from '@/contexts/AuthContext';
// import Input from '@/components/field/Input';
// import Button from '@/components/field/Button';
// import Card from '@/components/common/Card';
// import config from '@/config';
// import PandoraImage from '@/assets/pandora.png'; // Importing the graphic

// const LoginPage = () => {
//   const [username, setUsername] = useState(''); // Pre-filled username
//   const [password, setPassword] = useState(''); // Pre-filled masked password
//   const [error, setError] = useState('');
//   const [loading, setLoading] = useState(false);

//   const { login } = useAuth();
//   const navigate = useNavigate();

//   const handleSubmit = async (e) => {
//     e.preventDefault();

//     // Validate form
//     if (!username || !password) {
//       setError('Username and password are required');
//       return;
//     }

//     try {
//       setLoading(true);
//       setError('');

//       // Call login function from auth context
//       await login(username, password);

//       // Redirect to dashboard
//       navigate('/dashboard');
//     } catch (error) {
//       setError(error.error || 'Login failed. Please check your credentials.');
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <div className="min-h-screen bg-gray-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative">
//       {/* Decorative stars */}
//       <div className="absolute right-10 top-20">
//         <svg className="w-12 h-12 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
//           <path d="M12 2l2.4 7.2h7.6l-6 4.8 2.4 7.2-6-4.8-6 4.8 2.4-7.2-6-4.8h7.6z" />
//         </svg>
//       </div>
//       <div className="absolute right-20 top-10">
//         <svg className="w-8 h-8 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
//           <path d="M12 2l2.4 7.2h7.6l-6 4.8 2.4 7.2-6-4.8-6 4.8 2.4-7.2-6-4.8h7.6z" />
//         </svg>
//       </div>

//       <div className="flex flex-col md:flex-row items-center max-w-4xl w-full space-y-8 md:space-y-0 md:space-x-8 relative z-10">
//         {/* Login Form */}
//         <div className="w-full md:w-1/2">
//           <div className="text-center">
//             <h1 className="text-4xl font-extrabold text-gray-900 tracking-wider">PANDORA 2</h1>
//             <p className="mt-2 text-sm text-gray-600">Sign in to your account!</p>
//           </div>

//           <Card className="bg-gray-100 shadow-lg rounded-xl p-8 mt-6">
//             <form className="space-y-6" onSubmit={handleSubmit}>
//               {error && (
//                 <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4 rounded">
//                   <div className="flex">
//                     <div className="flex-shrink-0">
//                       <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
//                         <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
//                       </svg>
//                     </div>
//                     <div className="ml-3">
//                       <p className="text-sm text-red-700">{error}</p>
//                     </div>
//                   </div>
//                 </div>
//               )}

//               <Input
//                 id="username"
//                 name="username"
//                 type="text"
//                 label="Username*"
//                 value={username}
//                 onChange={(e) => setUsername(e.target.value)}
//                 required
//                 autoComplete="username"
//                 className="bg-white rounded-lg"
//               />

//               <Input
//                 id="password"
//                 name="password"
//                 type="password"
//                 label="Password*"
//                 value={password}
//                 onChange={(e) => setPassword(e.target.value)}
//                 required
//                 autoComplete="current-password"
//                 className="bg-white rounded-lg"
//               />

//               <div>
//                 <Button
//                   type="submit"
//                   disabled={loading}
//                   loading={loading}
//                   fullWidth={true}
//                   variant="primary"
//                   className="bg-black text-gray-800 hover:bg-gray-100 rounded-lg"
//                 >
//                   Sign In
//                 </Button>
//               </div>

//               <div className="text-center">
//                 <Link
//                   to="/forget-password"
//                   className="text-blue-600 hover:text-blue-500 text-sm font-medium transition-colors duration-300"
//                 >
//                   Forget your password?
//                 </Link>
//               </div>
//             </form>
//           </Card>
//         </div>

//         {/* Graphic from pandora.png */}
//         <div className="w-full md:w-1/2 flex justify-end items-end">
//         </div>
//         <div className="relative min-h-screen">
//   <img 
//     src={PandoraImage} 
//     alt="Pandora 2 Graphic" 
//     className="absolute bottom-[-200px] right-[-700px] top-[300px] max-w-[1050px] h-[700px]"
//   />
// </div>
//       </div>
//     </div>
//   );
// };

// export default LoginPage;





// /**
//  * Login Page
//  *
//  * This page handles user authentication for Pandora 2.
//  */

// import { useState } from 'react';
// import { useNavigate, Link } from 'react-router-dom';
// import { useAuth } from '@/contexts/AuthContext';
// import Input from '@/components/field/Input';
// import Button from '@/components/field/Button';
// import Card from '@/components/common/Card';
// import config from '@/config';
// import PandoraImage from '@/assets/pandora.png'; // Importing the graphic

// const LoginPage = () => {
//   const [username, setUsername] = useState('');
//   const [password, setPassword] = useState('');
//   const [error, setError] = useState('');
//   const [loading, setLoading] = useState(false);

//   const { login } = useAuth();
//   const navigate = useNavigate();

//   const handleSubmit = async (e) => {
//     e.preventDefault();

//     if (!username || !password) {
//       setError('Username and password are required');
//       return;
//     }

//     try {
//       setLoading(true);
//       setError('');
//       await login(username, password);
//       navigate('/dashboard');
//     } catch (error) {
//       setError(error.error || 'Login failed. Please check your credentials.');
//     } finally {
//       setLoading(false);
//     }
//   };

//   return (
//     <div className="min-h-screen bg-gray-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
    

//       <div className="flex flex-col md:flex-row items-center max-w-4xl w-full space-y-8 md:space-y-0 md:space-x-8 relative z-10">
//         {/* Login Form */}
//         <div className="w-full md:w-1/2">
//           <div className="text-center">
//             <h1 className="text-4xl font-extrabold text-gray-900 tracking-wider">PANDORA 2</h1>
//             <p className="mt-2 text-sm text-gray-600">Sign in to your account!</p>
//           </div>

//           <Card className="bg-gray-100 shadow-lg rounded-xl p-8 mt-6">
//             <form className="space-y-6" onSubmit={handleSubmit}>
//               {error && (
//                 <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4 rounded">
//                   <div className="flex">
//                     <div className="flex-shrink-0">
//                       <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
//                         <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
//                       </svg>
//                     </div>
//                     <div className="ml-3">
//                       <p className="text-sm text-red-700">{error}</p>
//                     </div>
//                   </div>
//                 </div>
//               )}

//               <Input
//                 id="username"
//                 name="username"
//                 type="text"
//                 label="Username*"
//                 value={username}
//                 onChange={(e) => setUsername(e.target.value)}
//                 required
//                 autoComplete="username"
//                 className="bg-white rounded-lg"
//               />

//               <Input
//                 id="password"
//                 name="password"
//                 type="password"
//                 label="Password*"
//                 value={password}
//                 onChange={(e) => setPassword(e.target.value)}
//                 required
//                 autoComplete="current-password"
//                 className="bg-white rounded-lg"
//               />

//               <div>
//                 <Button
//                   type="submit"
//                   disabled={loading}
//                   loading={loading}
//                   fullWidth={true}
//                   variant="primary"
//                   className="bg-black text-gray-800 hover:bg-gray-100 rounded-lg"
//                 >
//                   Sign In
//                 </Button>
//               </div>

//               <div className="text-center">
//                 <Link
//                   to="/forget-password"
//                   className="text-blue-600 hover:text-blue-500 text-sm font-medium transition-colors duration-300"
//                 >
//                   Forget your password?
//                 </Link>
//               </div>
//             </form>
//           </Card>
//         </div>

//         {/* Graphic from pandora.png */}
//         <div className="w-full md:w-1/2 relative">
//           <img 
//             src={PandoraImage} 
//             alt="Pandora 2 Graphic" 
//             className="absolute bottom-[-200px] right-[-800px] top-[-300px] max-w-[1200px] h-[900px]"
//           />
//         </div>
//       </div>
//     </div>
//   );
// };

// export default LoginPage;





/**
 * Login Page
 *
 * This page handles user authentication for Pandora 2.
 */

import { useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import Input from '@/components/field/Input';
import Button from '@/components/field/Button';
import Card from '@/components/common/Card';
import config from '@/config';
import PandoraImage from '@/assets/pandora.png'; // Importing the graphic

const LoginPage = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const { login } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!username || !password) {
      setError('Username and password are required');
      return;
    }

    try {
      setLoading(true);
      setError('');
      await login(username, password);
      navigate('/dashboard');
    } catch (error) {
      setError(error.error || 'Login failed. Please check your credentials.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center py-6 sm:py-8 md:py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
      <div className="flex flex-col md:flex-row items-center max-w-7xl w-full space-y-8 md:space-y-0 md:space-x-8 relative z-10">
        {/* Login Form */}
        <div className="w-full md:w-1/2">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-extrabold text-gray-900 tracking-wider">
              PANDORA 2
            </h1>
            <p className="mt-2 sm:mt-3 md:mt-4 text-base sm:text-lg md:text-xl text-gray-600">
              Sign in to your account!
            </p>
          </div>

          <Card className="bg-gray-100 shadow-xl rounded-2xl p-6 sm:p-8 md:p-10 mt-6 sm:mt-8">
            <form className="space-y-6 sm:space-y-8" onSubmit={handleSubmit}>
              {error && (
                <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4 rounded">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 sm:h-6 sm:w-6 text-red-500"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm sm:text-base text-red-700">{error}</p>
                    </div>
                  </div>
                </div>
              )}

              <Input
                id="username"
                name="username"
                type="text"
                label="Username*"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                required
                autoComplete="username"
                className="bg-white rounded-lg text-base sm:text-lg py-2 sm:py-3"
              />

              <Input
                id="password"
                name="password"
                type="password"
                label="Password*"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
                autoComplete="current-password"
                className="bg-white rounded-lg text-base sm:text-lg py-2 sm:py-3"
              />

              <div>
                <Button
                  type="submit"
                  disabled={loading}
                  loading={loading}
                  fullWidth={true}
                  variant="primary"
                  className="bg-black text-white hover:bg-gray-800 rounded-lg text-base sm:text-lg py-2 sm:py-3"
                >
                  Sign In
                </Button>
              </div>

              <div className="text-center">
                <Link
                  to="/forget-password"
                  className="text-blue-600 hover:text-blue-500 text-sm sm:text-base font-medium transition-colors duration-300"
                >
                  Forget your password?
                </Link>
              </div>
            </form>
          </Card>
        </div>

        {/* Graphic from pandora.png */}
        <div className="w-full md:w-1/2 relative">
          <img
            src={PandoraImage}
            alt="Pandora 2 Graphic"
            className="absolute top-[-250px] sm:bottom-[-200] right-[-700px] max-w-[80vw] sm:max-w-[600px] md:max-w-[800px] lg:max-w-[1200px] h-[300px] sm:h-[500px] md:h-[700px] lg:h-[900px] object-contain"
          />
        </div>
      </div>
    </div>
  );
};

export default LoginPage;