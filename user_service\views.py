"""
Views for the User Service.

This module defines the API endpoints for the User Service.

English: This file defines the API endpoints for the User Service
Tanglish: Indha file User Service-kku API endpoints-a define pannum
"""

from flask import Blueprint, jsonify, request
from user_service.controllers import register_user, get_users, get_user

# Create a Blueprint for user routes
user_bp = Blueprint('user', __name__)

# Add route handlers for OPTIONS requests
@user_bp.route('/<path:path>', methods=['OPTIONS'])
def handle_options_path(path):
    """
    Handle OPTIONS requests for CORS preflight with path.

    Returns:
        Empty response with appropriate CORS headers

    English: This endpoint handles OPTIONS requests for CORS
    Tanglish: Indha endpoint CORS-kku OPTIONS requests-a handle pannum
    """
    return "", 200

@user_bp.route('/', methods=['OPTIONS'])
def handle_options_root():
    """
    Handle OPTIONS requests for CORS preflight at root.

    Returns:
        Empty response with appropriate CORS headers

    English: This endpoint handles OPTIONS requests for CORS at root
    Tanglish: Indha endpoint CORS-kku OPTIONS requests-a root-la handle pannum
    """
    return "", 200

@user_bp.route('/register', methods=['POST', 'OPTIONS'])
def register_route():
    """
    Register endpoint.

    Returns:
        Response from register_user controller

    English: This endpoint handles user registration
    Tanglish: Indha endpoint user registration-a handle pannum
    """
    if request.method == 'OPTIONS':
        # Handle OPTIONS request for CORS preflight
        return "", 200
    return register_user()

@user_bp.route('/users', methods=['GET', 'OPTIONS'])
def get_users_route():
    """
    Get all users endpoint.

    Returns:
        Response from get_users controller

    English: This endpoint gets all users
    Tanglish: Indha endpoint ella users-um get pannum
    """
    if request.method == 'OPTIONS':
        # Handle OPTIONS request for CORS preflight
        return "", 200
    return get_users()

@user_bp.route('/users/<int:user_id>', methods=['GET', 'OPTIONS'])
def get_user_route(user_id):
    """
    Get a specific user endpoint.

    Args:
        user_id: ID of the user to get

    Returns:
        Response from get_user controller

    English: This endpoint gets a specific user
    Tanglish: Indha endpoint specific user-a get pannum
    """
    if request.method == 'OPTIONS':
        # Handle OPTIONS request for CORS preflight
        return "", 200
    return get_user(user_id)

@user_bp.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint.

    Returns:
        JSON response indicating the service is running

    English: This endpoint checks if the service is running
    Tanglish: Indha endpoint service odi kondu irukka nu check pannum
    """
    return jsonify({"status": "healthy", "service": "user"})
