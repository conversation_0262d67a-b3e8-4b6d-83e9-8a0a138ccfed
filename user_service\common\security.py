"""
Security utilities for input validation and sanitization.

This module provides functions to prevent SQL injection, XSS, and other security vulnerabilities.

English: This file contains security functions to protect against attacks
Tanglish: Indha file-la attacks-a prevent panna security functions irukku
"""

import re
import html
from typing import Any, Dict, List, Optional

# Dangerous SQL keywords and patterns
SQL_INJECTION_PATTERNS = [
    r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE|UNION|SCRIPT)\b)",
    r"(--|#|/\*|\*/)",
    r"(\b(OR|AND)\s+\d+\s*=\s*\d+)",
    r"(\b(OR|AND)\s+['\"]?\w+['\"]?\s*=\s*['\"]?\w+['\"]?)",
    r"(;|\|\||&&)",
    r"(\bUNION\s+SELECT\b)",
    r"(\bDROP\s+TABLE\b)",
    r"(\bEXEC\s*\()",
    r"(\bxp_cmdshell\b)"
]

# XSS patterns
XSS_PATTERNS = [
    r"<script[^>]*>.*?</script>",
    r"javascript:",
    r"vbscript:",
    r"onload\s*=",
    r"onerror\s*=",
    r"onclick\s*=",
    r"onmouseover\s*=",
    r"onfocus\s*=",
    r"onblur\s*=",
    r"<iframe[^>]*>",
    r"<object[^>]*>",
    r"<embed[^>]*>",
    r"<link[^>]*>",
    r"<meta[^>]*>",
    r"<style[^>]*>.*?</style>",
    r"expression\s*\(",
    r"url\s*\(",
    r"@import"
]

def sanitize_string(input_string: str, max_length: Optional[int] = None) -> str:
    """
    Sanitize a string to prevent XSS attacks.
    
    Args:
        input_string: The string to sanitize
        max_length: Maximum allowed length (optional)
        
    Returns:
        Sanitized string
        
    English: This function cleans a string to prevent XSS attacks
    Tanglish: Indha function string-a clean panni XSS attacks-a prevent pannum
    """
    if not isinstance(input_string, str):
        return str(input_string)
    
    # Trim whitespace
    sanitized = input_string.strip()
    
    # Limit length if specified
    if max_length and len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    # HTML encode to prevent XSS
    sanitized = html.escape(sanitized, quote=True)
    
    # Remove any remaining dangerous patterns
    for pattern in XSS_PATTERNS:
        sanitized = re.sub(pattern, '', sanitized, flags=re.IGNORECASE)
    
    return sanitized

def validate_against_sql_injection(input_string: str) -> bool:
    """
    Check if a string contains SQL injection patterns.
    
    Args:
        input_string: The string to check
        
    Returns:
        True if the string is safe, False if it contains SQL injection patterns
        
    English: This function checks if a string has SQL injection patterns
    Tanglish: Indha function string-la SQL injection patterns irukka nu check pannum
    """
    if not isinstance(input_string, str):
        return True
    
    # Check against SQL injection patterns
    for pattern in SQL_INJECTION_PATTERNS:
        if re.search(pattern, input_string, re.IGNORECASE):
            return False
    
    return True

def validate_email(email: str) -> bool:
    """
    Validate email format.
    
    Args:
        email: Email address to validate
        
    Returns:
        True if email is valid, False otherwise
        
    English: This function checks if an email address is valid
    Tanglish: Indha function email address valid-a irukka nu check pannum
    """
    if not isinstance(email, str):
        return False
    
    # Basic email regex pattern
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    
    # Check length
    if len(email) > 254:  # RFC 5321 limit
        return False
    
    # Check pattern
    if not re.match(email_pattern, email):
        return False
    
    # Check for dangerous patterns
    if not validate_against_sql_injection(email):
        return False
    
    return True

def validate_username(username: str) -> bool:
    """
    Validate username format.
    
    Args:
        username: Username to validate
        
    Returns:
        True if username is valid, False otherwise
        
    English: This function checks if a username is valid
    Tanglish: Indha function username valid-a irukka nu check pannum
    """
    if not isinstance(username, str):
        return False
    
    # Check length (3-50 characters)
    if len(username) < 3 or len(username) > 50:
        return False
    
    # Allow only alphanumeric characters, underscores, and hyphens
    if not re.match(r'^[a-zA-Z0-9_-]+$', username):
        return False
    
    # Check for SQL injection patterns
    if not validate_against_sql_injection(username):
        return False
    
    return True

def validate_password(password: str) -> Dict[str, Any]:
    """
    Validate password strength.
    
    Args:
        password: Password to validate
        
    Returns:
        Dictionary with validation results
        
    English: This function checks if a password is strong enough
    Tanglish: Indha function password strong-a irukka nu check pannum
    """
    result = {
        'valid': True,
        'errors': []
    }
    
    if not isinstance(password, str):
        result['valid'] = False
        result['errors'].append("Password must be a string")
        return result
    
    # Check minimum length
    if len(password) < 8:
        result['valid'] = False
        result['errors'].append("Password must be at least 8 characters long")
    
    # Check maximum length
    if len(password) > 128:
        result['valid'] = False
        result['errors'].append("Password must be less than 128 characters long")
    
    # Check for at least one uppercase letter
    if not re.search(r'[A-Z]', password):
        result['valid'] = False
        result['errors'].append("Password must contain at least one uppercase letter")
    
    # Check for at least one lowercase letter
    if not re.search(r'[a-z]', password):
        result['valid'] = False
        result['errors'].append("Password must contain at least one lowercase letter")
    
    # Check for at least one digit
    if not re.search(r'\d', password):
        result['valid'] = False
        result['errors'].append("Password must contain at least one digit")
    
    # Check for at least one special character
    if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
        result['valid'] = False
        result['errors'].append("Password must contain at least one special character")
    
    return result

def sanitize_user_input(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Sanitize user input data.
    
    Args:
        data: Dictionary of user input data
        
    Returns:
        Sanitized data dictionary
        
    English: This function cleans user input data
    Tanglish: Indha function user input data-va clean pannum
    """
    sanitized_data = {}
    
    for key, value in data.items():
        if isinstance(value, str):
            # Sanitize string values
            if key == 'email':
                # Don't HTML encode emails, just validate
                sanitized_data[key] = value.strip().lower()
            elif key == 'password':
                # Don't sanitize passwords, just validate
                sanitized_data[key] = value
            else:
                # Sanitize other string fields
                sanitized_data[key] = sanitize_string(value, max_length=255)
        else:
            # Keep non-string values as is
            sanitized_data[key] = value
    
    return sanitized_data

def validate_user_registration_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate user registration data.
    
    Args:
        data: User registration data
        
    Returns:
        Validation result dictionary
        
    English: This function validates user registration data
    Tanglish: Indha function user registration data-va validate pannum
    """
    result = {
        'valid': True,
        'errors': []
    }
    
    # Validate username
    if 'username' in data:
        if not validate_username(data['username']):
            result['valid'] = False
            result['errors'].append("Invalid username. Must be 3-50 characters, alphanumeric, underscore, or hyphen only")
    
    # Validate email
    if 'email' in data:
        if not validate_email(data['email']):
            result['valid'] = False
            result['errors'].append("Invalid email address format")
    
    # Validate password
    if 'password' in data:
        password_validation = validate_password(data['password'])
        if not password_validation['valid']:
            result['valid'] = False
            result['errors'].extend(password_validation['errors'])
    
    # Validate role
    if 'role' in data:
        valid_roles = ['Super Admin', 'Admin', 'Teacher', 'Student', 'Parent']
        if data['role'] not in valid_roles:
            result['valid'] = False
            result['errors'].append(f"Invalid role. Must be one of: {', '.join(valid_roles)}")
    
    return result
