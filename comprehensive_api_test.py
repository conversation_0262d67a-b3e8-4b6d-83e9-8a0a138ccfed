#!/usr/bin/env python3
"""
Comprehensive API Testing Script for School Management System
This script tests all endpoints across all microservices systematically.
"""

import requests
import json
import sys
from datetime import datetime

# Base URLs for all services
BASE_URLS = {
    'auth': 'http://localhost:5000/api/auth',
    'users': 'http://localhost:5001/api/users', 
    'students': 'http://localhost:5002/api/students',
    'courses': 'http://localhost:5003/api/courses',
    'parents': 'http://localhost:5004/api/parents'
}

# Global variables
access_token = None
test_results = []

def log_test(endpoint, method, status_code, response_data, error=None):
    """Log test results"""
    result = {
        'timestamp': datetime.now().isoformat(),
        'endpoint': endpoint,
        'method': method,
        'status_code': status_code,
        'response': response_data,
        'error': error,
        'success': status_code < 400
    }
    test_results.append(result)
    
    status = "✅ PASS" if result['success'] else "❌ FAIL"
    print(f"{status} {method} {endpoint} - Status: {status_code}")
    if error:
        print(f"   Error: {error}")
    return result

def make_request(method, url, data=None, headers=None, auth_required=True):
    """Make HTTP request with proper headers"""
    if headers is None:
        headers = {'Content-Type': 'application/json'}
    
    if auth_required and access_token:
        headers['Authorization'] = f'Bearer {access_token}'
    
    try:
        if method.upper() == 'GET':
            response = requests.get(url, headers=headers)
        elif method.upper() == 'POST':
            response = requests.post(url, json=data, headers=headers)
        elif method.upper() == 'PUT':
            response = requests.put(url, json=data, headers=headers)
        elif method.upper() == 'DELETE':
            response = requests.delete(url, headers=headers)
        else:
            raise ValueError(f"Unsupported method: {method}")
        
        try:
            response_data = response.json()
        except:
            response_data = response.text
            
        return log_test(url, method, response.status_code, response_data)
        
    except Exception as e:
        return log_test(url, method, 0, None, str(e))

def test_authentication():
    """Test authentication endpoints"""
    print("\n🔐 Testing Authentication Service...")
    
    # Test health check
    make_request('GET', f"{BASE_URLS['auth']}/health", auth_required=False)
    
    # Test login with super admin credentials
    login_data = {
        "username": "superadmin",
        "password": "D@qwertyuiop"
    }
    
    result = make_request('POST', f"{BASE_URLS['auth']}/login", login_data, auth_required=False)
    
    if result['success'] and 'token' in result['response']:
        global access_token
        access_token = result['response']['token']
        print(f"   🎫 Token obtained: {access_token[:50]}...")
        
        # Test token verification
        make_request('GET', f"{BASE_URLS['auth']}/verify")
    else:
        print("   ❌ Failed to obtain access token!")
        return False
    
    return True

def test_user_service():
    """Test user service endpoints"""
    print("\n👥 Testing User Service...")
    
    # Test health check
    make_request('GET', f"{BASE_URLS['users']}/health", auth_required=False)
    
    # Test get all users
    make_request('GET', f"{BASE_URLS['users']}/users")
    
    # Test user registration
    user_data = {
        "username": f"testuser_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "email": f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
        "password": "TestPassword123!",
        "role": "Teacher"
    }
    
    result = make_request('POST', f"{BASE_URLS['users']}/register", user_data)
    
    # If user creation was successful, test getting specific user
    if result['success'] and 'user' in result['response']:
        user_id = result['response']['user']['id']
        make_request('GET', f"{BASE_URLS['users']}/users/{user_id}")

def test_student_service():
    """Test student service endpoints"""
    print("\n🎓 Testing Student Service...")

    # Test health check
    make_request('GET', f"{BASE_URLS['students']}/health", auth_required=False)

    # Test get all students
    make_request('GET', f"{BASE_URLS['students']}/students")

    # First create a user for the student
    user_data = {
        "username": f"student_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "email": f"student_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
        "password": "StudentPass123!",
        "role": "Student"
    }

    user_result = make_request('POST', f"{BASE_URLS['users']}/register", user_data)

    if user_result['success'] and 'user' in user_result['response']:
        user_id = user_result['response']['user']['id']

        # Now create student profile with the user_id
        student_data = {
            "user_id": user_id,
            "first_name": "Test",
            "last_name": "Student",
            "date_of_birth": "2005-01-15",
            "address": "123 Test Street",
            "phone": "**********"
        }

        result = make_request('POST', f"{BASE_URLS['students']}/students", student_data)

        # If student creation was successful, test getting specific student
        if result['success'] and 'student' in result['response']:
            student_id = result['response']['student']['id']
            make_request('GET', f"{BASE_URLS['students']}/students/{student_id}")
            return student_id  # Return for use in mapping tests

    return None

def test_parent_service():
    """Test parent service endpoints"""
    print("\n👨‍👩‍👧‍👦 Testing Parent Service...")

    # Test health check
    make_request('GET', f"{BASE_URLS['parents']}/health", auth_required=False)

    # Test get all parents
    make_request('GET', f"{BASE_URLS['parents']}/parents")

    # First create a user for the parent
    user_data = {
        "username": f"parent_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "email": f"parent_user_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
        "password": "ParentPass123!",
        "role": "Parent"
    }

    user_result = make_request('POST', f"{BASE_URLS['users']}/register", user_data)

    if user_result['success'] and 'user' in user_result['response']:
        user_id = user_result['response']['user']['id']

        # Now create parent profile with the user_id
        parent_data = {
            "user_id": user_id,
            "first_name": "Test",
            "last_name": "Parent",
            "occupation": "Engineer",
            "address": "456 Test Avenue",
            "phone": "**********"
        }

        result = make_request('POST', f"{BASE_URLS['parents']}/parents", parent_data)

        # If parent creation was successful, test getting specific parent
        if result['success'] and 'parent' in result['response']:
            parent_id = result['response']['parent']['id']
            make_request('GET', f"{BASE_URLS['parents']}/parents/{parent_id}")
            return parent_id  # Return for use in mapping tests

    return None

def test_course_service():
    """Test course service endpoints"""
    print("\n📚 Testing Course Service...")
    
    # Test health check
    make_request('GET', f"{BASE_URLS['courses']}/health", auth_required=False)
    
    # Test get all courses
    make_request('GET', f"{BASE_URLS['courses']}/courses")
    
    # Test course creation
    course_data = {
        "name": f"Test Course {datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "code": f"TC{datetime.now().strftime('%Y%m%d%H%M%S')}",
        "description": "Test course for API testing",
        "credits": 3
    }
    
    result = make_request('POST', f"{BASE_URLS['courses']}/courses", course_data)
    
    # If course creation was successful, test getting specific course
    if result['success'] and 'course' in result['response']:
        course_id = result['response']['course']['id']
        make_request('GET', f"{BASE_URLS['courses']}/courses/{course_id}")
        return course_id  # Return for use in mapping tests

    return None

def test_relationship_mappings(student_id=None, parent_id=None, course_id=None):
    """Test relationship mapping endpoints"""
    print("\n🔗 Testing Relationship Mappings...")

    # Test parent-student mapping with real IDs if available
    if student_id and parent_id:
        mapping_data = {
            "student_id": student_id,
            "parent_id": parent_id,
            "relationship": "Father"
        }
        make_request('POST', f"{BASE_URLS['students']}/map-parent", mapping_data)

        # Test getting students for this parent
        make_request('GET', f"{BASE_URLS['students']}/parent-students/{parent_id}")
    else:
        # Test with non-existent IDs to check error handling
        mapping_data = {
            "student_id": 999,  # Non-existent ID
            "parent_id": 999    # Non-existent ID
        }
        make_request('POST', f"{BASE_URLS['students']}/map-parent", mapping_data)

    # Test student-course mapping with real IDs if available
    if student_id and course_id:
        course_mapping_data = {
            "student_id": student_id,
            "course_id": course_id
        }
        make_request('POST', f"{BASE_URLS['courses']}/map-student", course_mapping_data)

        # Test getting courses for this student
        make_request('GET', f"{BASE_URLS['courses']}/student-courses/{student_id}")
    else:
        # Test with non-existent IDs to check error handling
        course_mapping_data = {
            "student_id": 999,  # Non-existent ID
            "course_id": 999    # Non-existent ID
        }
        make_request('POST', f"{BASE_URLS['courses']}/map-student", course_mapping_data)

def generate_report():
    """Generate comprehensive test report"""
    print("\n" + "="*80)
    print("📊 COMPREHENSIVE API TEST REPORT")
    print("="*80)
    
    total_tests = len(test_results)
    passed_tests = len([r for r in test_results if r['success']])
    failed_tests = total_tests - passed_tests
    
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests} ✅")
    print(f"Failed: {failed_tests} ❌")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print("\n📋 DETAILED RESULTS:")
    print("-" * 80)
    
    for result in test_results:
        status = "✅" if result['success'] else "❌"
        print(f"{status} {result['method']} {result['endpoint']}")
        print(f"   Status: {result['status_code']}")
        if result['error']:
            print(f"   Error: {result['error']}")
        print()
    
    # Save detailed report to file
    with open('api_test_report.json', 'w') as f:
        json.dump(test_results, f, indent=2)
    
    print(f"📄 Detailed report saved to: api_test_report.json")

def main():
    """Main test execution"""
    print("🚀 Starting Comprehensive API Testing...")
    print("="*80)
    
    # Step 1: Authentication
    if not test_authentication():
        print("❌ Authentication failed. Cannot proceed with other tests.")
        return
    
    # Step 2: Test all services and collect IDs for mapping tests
    test_user_service()
    student_id = test_student_service()
    parent_id = test_parent_service()
    course_id = test_course_service()
    test_relationship_mappings(student_id, parent_id, course_id)
    
    # Step 3: Generate report
    generate_report()

if __name__ == "__main__":
    main()
