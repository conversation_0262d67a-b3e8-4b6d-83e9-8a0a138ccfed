# School Management System

A microservices-based school management system built with Flask and React.

## Features

- Microservices architecture with Flask
- JWT authentication
- Role-based access control using middleware
- PostgreSQL databases
- React frontend

## Microservices

1. **Auth Service**: Handles login and JWT token generation
2. **User Service**: Manages user registration and roles
3. **Course Service**: Manages courses and student mappings
4. **Student Service**: Manages student details and parent mappings
5. **Parent Service**: Manages parent details and student mappings

## Prerequisites

- Python 3.8+
- PostgreSQL
- Node.js (optional, for running the frontend with a server)

## Setup

### 1. Clone the repository

```bash
git clone <repository-url>
cd <repository-directory>
```

### 2. Create and activate a virtual environment

```bash
python -m venv venv
# On Windows
venv\Scripts\activate
# On macOS/Linux
source venv/bin/activate
```

### 3. Install dependencies

```bash
pip install -r requirements.txt
```

### 4. Set up PostgreSQL databases

Create the following databases in PostgreSQL:

- auth_db
- user_db
- course_db
- student_db
- parent_db

### 5. Configure environment variables

Edit the `.env` file with your database credentials and other settings.

### 6. Initialize the databases

Each service will create its tables when it starts for the first time.

### 7. Add a Super Admin user

Use the provided script to register a Super Admin user:

```bash
python register_super_admin.py --username superadmin --password yourpassword --email <EMAIL>
```

This script will create the Super Admin user in both the `auth_db` and `user_db` databases.

## Running the Application

### 1. Start the microservices

Open 5 separate terminal windows and run each service:

```bash
# Terminal 1 - Auth Service
python auth_service/app.py

# Terminal 2 - User Service
python user_service/app.py

# Terminal 3 - Course Service
python course_service/app.py

# Terminal 4 - Student Service
python student_service/app.py

# Terminal 5 - Parent Service
python parent_service/app.py
```

### 2. Serve the frontend

You can use any static file server to serve the frontend. For example, with Python:

```bash
cd frontend
python -m http.server 8000
```

Then open http://localhost:8000 in your browser.

## API Endpoints

### Auth Service (http://localhost:5000)

- POST `/api/auth/login`: Login with username and password
- GET `/api/auth/verify`: Verify a JWT token

### User Service (http://localhost:5001)

- POST `/api/users/register`: Register a new user
- GET `/api/users/users`: Get all users
- GET `/api/users/users/{user_id}`: Get a specific user

### Course Service (http://localhost:5003)

- POST `/api/courses/courses`: Create a new course
- GET `/api/courses/courses`: Get all courses
- GET `/api/courses/courses/{course_id}`: Get a specific course
- POST `/api/courses/map-student`: Map a student to a course
- GET `/api/courses/student-courses/{student_id}`: Get all courses for a student

### Student Service (http://localhost:5002)

- POST `/api/students/students`: Register a new student
- GET `/api/students/students`: Get all students
- GET `/api/students/students/{student_id}`: Get a specific student
- POST `/api/students/map-parent`: Map a parent to a student
- GET `/api/students/parent-students/{parent_id}`: Get all students for a parent

### Parent Service (http://localhost:5004)

- POST `/api/parents/parents`: Register a new parent
- GET `/api/parents/parents`: Get all parents
- GET `/api/parents/parents/{parent_id}`: Get a specific parent
- GET `/api/parents/parents/user/{user_id}`: Get a parent by user ID

## User Roles

1. **Super Admin**: Can register Admins, Teachers, Students, and Parents, manage courses
2. **Admin**: Can register Teachers, Students, and Parents
3. **Teacher**: Can register Students and Parents, map them to Courses, and map Parents to Students
4. **Student**: Can view their courses
5. **Parent**: Can view their children's details and courses

All user types are stored in the same database table (`users`) with a `role` field to distinguish between them.

## License

[MIT License](LICENSE)
