"""
<PERSON><PERSON><PERSON> to test the student service endpoints.

This script tests the student service endpoints by getting all students.
"""

import requests
import jwt
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get JWT secret from environment variables
JWT_SECRET = os.environ.get('JWT_SECRET', 'simple-jwt-secret-for-school-management-system')

def generate_token(user_id, username, role, is_admin=False):
    """
    Generate a JWT token.
    
    Args:
        user_id: User ID
        username: Username
        role: User role
        is_admin: Whether the user is an admin
        
    Returns:
        JWT token string
    """
    # Set token expiration time (e.g., 24 hours)
    expiration = datetime.utcnow() + timedelta(hours=24)
    
    # Create token payload
    payload = {
        'sub': str(user_id),  # Convert user ID to string
        'username': username,
        'role': role,
        'is_admin': is_admin,
        'exp': expiration
    }
    
    # Generate token
    token = jwt.encode(payload, JWT_SECRET, algorithm='HS256')
    
    return token

def get_students(token):
    """
    Get all students.
    
    Args:
        token: JWT token string
        
    Returns:
        Response from the endpoint
    """
    url = 'http://localhost:5002/api/students/students'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    print(f"Sending GET request to {url}")
    print(f"Headers: {headers}")
    
    response = requests.get(url, headers=headers)
    
    print(f"Response status code: {response.status_code}")
    print(f"Response content: {response.text}")
    
    return response

if __name__ == '__main__':
    # Test with a student token
    student_token = generate_token(13, 'student1', 'Student', False)
    print(f"Generated student token: {student_token}")
    
    print("\n=== Testing GET /api/students/students as Student ===")
    get_students(student_token)
    
    # Test with a super admin token
    admin_token = generate_token(9, 'superadmin', 'Super Admin', True)
    print(f"\nGenerated admin token: {admin_token}")
    
    print("\n=== Testing GET /api/students/students as Super Admin ===")
    get_students(admin_token)
