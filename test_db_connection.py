"""
Test PostgreSQL connection.

This script tests the connection to PostgreSQL.
"""

import psycopg2
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Get database credentials from environment variables
db_user = os.environ.get('DB_USER', 'postgres')
db_password = os.environ.get('DB_PASSWORD', 'postgres')
db_host = os.environ.get('DB_HOST', 'localhost')
db_port = os.environ.get('DB_PORT', '5432')

# Try to connect to PostgreSQL
try:
    print(f"Trying to connect to PostgreSQL with user={db_user}, host={db_host}, port={db_port}")
    conn = psycopg2.connect(
        user=db_user,
        password=db_password,
        host=db_host,
        port=db_port,
        database="postgres"  # Connect to the default database
    )
    
    # Create a cursor
    cursor = conn.cursor()
    
    # Print PostgreSQL version
    cursor.execute("SELECT version();")
    db_version = cursor.fetchone()
    print(f"PostgreSQL version: {db_version}")
    
    # List all databases
    cursor.execute("SELECT datname FROM pg_database;")
    databases = cursor.fetchall()
    print("Existing databases:")
    for db in databases:
        print(f"  - {db[0]}")
    
    # Close the connection
    cursor.close()
    conn.close()
    print("PostgreSQL connection test successful!")
    
except Exception as e:
    print(f"Error connecting to PostgreSQL: {e}")
