"""
Controllers for the Course Service.

This module contains the business logic for course management.

English: This file contains the logic for course management
Tanglish: Indha file-la course management-kku logic irukku
"""

from flask import jsonify, request
from course_service.common.utils import handle_error, validate_request_data, call_service
from course_service.models import Course, StudentCourse
from course_service.common.db_config import db

def create_course():
    """
    Create a new course.

    Returns:
        JSON response with the created course

    English: This function creates a new course
    Tanglish: Indha function puthusa oru course-a create pannum
    """
    # Get request data
    data = request.get_json()
    if not data:
        return handle_error("No data provided", 400)

    # Check if required fields are present
    required_fields = ['name']
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        return handle_error(f"Missing required fields: {', '.join(missing_fields)}", 400)

    # Get user role from request environment
    user_role = request.environ.get('user_role')
    print(f"User role from environment: '{user_role}'")

    # Check if the user has permission to create a course
    if user_role not in ['Super Admin', 'Admin']:
        print(f"Permission denied. User role '{user_role}' cannot create a course.")
        return handle_error("You don't have permission to create a course", 403)

    print(f"Permission granted. User role '{user_role}' can create a course.")

    # Check if course name already exists
    if Course.query.filter_by(name=data['name']).first():
        return handle_error("Course name already exists", 400)

    # Create new course
    course = Course(
        name=data['name'],
        description=data.get('description')
    )

    # Save course to database
    db.session.add(course)
    db.session.commit()

    # Return course information
    return jsonify({
        "message": "Course created successfully",
        "course": course.to_dict()
    }), 201

def get_courses():
    """
    Get all courses.

    Returns:
        JSON response with all courses

    English: This function gets all courses
    Tanglish: Indha function ella courses-um get pannum
    """
    # Get all courses
    courses = Course.query.all()

    # Return courses
    return jsonify({
        "courses": [course.to_dict() for course in courses]
    })

def get_course(course_id):
    """
    Get a specific course.

    Args:
        course_id: ID of the course to get

    Returns:
        JSON response with the course

    English: This function gets a specific course
    Tanglish: Indha function specific course-a get pannum
    """
    # Get the course
    course = Course.query.get(course_id)
    if not course:
        return handle_error("Course not found", 404)

    # Return course
    return jsonify({
        "course": course.to_dict()
    })

def map_student_to_course():
    """
    Map a student to a course.

    Returns:
        JSON response with the mapping

    English: This function maps a student to a course
    Tanglish: Indha function oru student-a oru course-oda map pannum
    """
    # Get request data
    data = request.get_json()
    if not data:
        return handle_error("No data provided", 400)

    # Check if required fields are present
    required_fields = ['student_id', 'course_id']
    missing_fields = [field for field in required_fields if field not in data]
    if missing_fields:
        return handle_error(f"Missing required fields: {', '.join(missing_fields)}", 400)

    # Get user role from request environment
    user_role = request.environ.get('user_role')

    # Check if the user has permission to map a student to a course
    if user_role not in ['Super Admin', 'Admin', 'Teacher']:
        return handle_error("You don't have permission to map a student to a course", 403)

    # Check if the course exists
    course = Course.query.get(data['course_id'])
    if not course:
        return handle_error("Course not found", 404)

    # TEMPORARY FIX: Skip student service check for testing
    print(f"Bypassing student service check for student_id: {data['student_id']}")

    # In a production environment, you would uncomment the following code:
    """
    # Check if the student exists by calling the Student Service
    student_service_url = request.environ.get('STUDENT_SERVICE_URL', 'http://localhost:5002')
    student_url = f"{student_service_url}/api/students/students/{data['student_id']}"

    # Get the Authorization header from the request
    auth_header = request.headers.get('Authorization')
    headers = {'Authorization': auth_header} if auth_header else None

    # Call the Student Service
    response = call_service(student_url, headers=headers)
    if not response or response.status_code != 200:
        return handle_error("Student not found or service unavailable", 404)
    """

    # Check if the mapping already exists
    if StudentCourse.query.filter_by(
        student_id=data['student_id'],
        course_id=data['course_id']
    ).first():
        return handle_error("Student is already mapped to this course", 400)

    # Create new mapping
    student_course = StudentCourse(
        student_id=data['student_id'],
        course_id=data['course_id']
    )

    # Save mapping to database
    db.session.add(student_course)
    db.session.commit()

    # Return mapping information
    return jsonify({
        "message": "Student mapped to course successfully",
        "mapping": student_course.to_dict()
    }), 201

def get_student_courses(student_id):
    """
    Get all courses for a specific student.

    Args:
        student_id: ID of the student

    Returns:
        JSON response with the student's courses

    English: This function gets all courses for a specific student
    Tanglish: Indha function specific student-kku ella courses-um get pannum
    """
    # Get all mappings for the student
    mappings = StudentCourse.query.filter_by(student_id=student_id).all()

    # Return courses
    return jsonify({
        "courses": [mapping.to_dict() for mapping in mappings]
    })
