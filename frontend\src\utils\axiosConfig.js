/**
 * Axios Configuration
 *
 * This file configures Axios for the application.
 *
 * English: This file sets up Axios with default settings
 * Tanglish: Indha file Axios-a default settings-oda setup pannum
 */

import axios from 'axios';

// Configure Axios defaults
axios.defaults.withCredentials = true;
axios.defaults.headers.common['Content-Type'] = 'application/json';
axios.defaults.headers.common['Accept'] = 'application/json';

// Add a request interceptor to handle errors
axios.interceptors.request.use(
  config => {
    // Do not add CORS headers here - they should be set by the server
    return config;
  },
  error => {
    console.error('Axios request error:', error);
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle errors
axios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx
      console.error('Response error:', error.response.status, error.response.data);
    } else if (error.request) {
      // The request was made but no response was received
      console.error('Request error:', error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      console.error('Error:', error.message);
    }
    return Promise.reject(error);
  }
);

export default axios;
