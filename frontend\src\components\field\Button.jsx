/**
 * <PERSON><PERSON> Component
 *
 * A reusable button component with role-specific styling and various button types.
 * Supports role-specific styling for different user panels (super_admin, admin, teacher, student, parent).
 */

import { useState } from 'react';

const Button = ({
  children,
  type = 'button',
  variant = 'primary', // primary, secondary, danger, success, warning, info
  size = 'md', // sm, md, lg
  disabled = false,
  loading = false,
  onClick,
  className = '',
  userRole = '', // super_admin, admin, teacher, student, parent
  fullWidth = false,
  ...props
}) => {
  const [isPressed, setIsPressed] = useState(false);

  const handleMouseDown = () => setIsPressed(true);
  const handleMouseUp = () => setIsPressed(false);
  const handleMouseLeave = () => setIsPressed(false);

  // Role-specific color schemes
  const getRoleColors = () => {
    switch (userRole) {
      case 'super_admin':
        return {
          primary: 'bg-purple-600 hover:bg-purple-700 focus:ring-purple-500 text-white',
          secondary: 'bg-purple-100 hover:bg-purple-200 focus:ring-purple-500 text-purple-800 border border-purple-300',
          danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white',
          success: 'bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white',
          warning: 'bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500 text-white',
          info: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white'
        };
      case 'admin':
        return {
          primary: 'bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white',
          secondary: 'bg-green-100 hover:bg-green-200 focus:ring-green-500 text-green-800 border border-green-300',
          danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white',
          success: 'bg-emerald-600 hover:bg-emerald-700 focus:ring-emerald-500 text-white',
          warning: 'bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500 text-white',
          info: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white'
        };
      case 'teacher':
        return {
          primary: 'bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500 text-white',
          secondary: 'bg-indigo-100 hover:bg-indigo-200 focus:ring-indigo-500 text-indigo-800 border border-indigo-300',
          danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white',
          success: 'bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white',
          warning: 'bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500 text-white',
          info: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white'
        };
      case 'student':
        return {
          primary: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white',
          secondary: 'bg-blue-100 hover:bg-blue-200 focus:ring-blue-500 text-blue-800 border border-blue-300',
          danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white',
          success: 'bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white',
          warning: 'bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500 text-white',
          info: 'bg-cyan-600 hover:bg-cyan-700 focus:ring-cyan-500 text-white'
        };
      case 'parent':
        return {
          primary: 'bg-orange-600 hover:bg-orange-700 focus:ring-orange-500 text-white',
          secondary: 'bg-orange-100 hover:bg-orange-200 focus:ring-orange-500 text-orange-800 border border-orange-300',
          danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white',
          success: 'bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white',
          warning: 'bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500 text-white',
          info: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white'
        };
      default:
        return {
          primary: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white',
          secondary: 'bg-gray-200 hover:bg-gray-300 focus:ring-gray-500 text-gray-800 border border-gray-300',
          danger: 'bg-red-600 hover:bg-red-700 focus:ring-red-500 text-white',
          success: 'bg-green-600 hover:bg-green-700 focus:ring-green-500 text-white',
          warning: 'bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500 text-white',
          info: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 text-white'
        };
    }
  };

  // Size variants
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm';
      case 'lg':
        return 'px-6 py-3 text-lg';
      default:
        return 'px-4 py-2 text-base';
    }
  };

  const roleColors = getRoleColors();
  const colorClasses = roleColors[variant] || roleColors.primary;
  const sizeClasses = getSizeClasses();

  return (
    <button
      type={type}
      disabled={disabled || loading}
      onClick={onClick}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseLeave}
      className={`
        ${fullWidth ? 'w-full' : ''}
        ${sizeClasses}
        ${colorClasses}
        font-medium rounded-md shadow-sm
        focus:outline-none focus:ring-2 focus:ring-offset-2
        transition-all duration-200
        ${disabled || loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${isPressed ? 'transform scale-95' : ''}
        ${className}
      `}
      {...props}
    >
      {loading ? (
        <div className="flex items-center justify-center">
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            ></circle>
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          Loading...
        </div>
      ) : (
        children
      )}
    </button>
  );
};

export default Button;
