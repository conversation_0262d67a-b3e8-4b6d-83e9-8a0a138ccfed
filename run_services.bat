@echo off
echo Starting School Management System Microservices...

REM Start Auth Service
start cmd /k "title Auth Service && python -m auth_service.app"

REM Start User Service
start cmd /k "title User Service && python -m user_service.app"

REM Start Student Service
start cmd /k "title Student Service && python -m student_service.app"

REM Start Course Service
start cmd /k "title Course Service && python -m course_service.app"

REM Start Parent Service
start cmd /k "title Parent Service && python -m parent_service.app"

echo All services started!
echo Auth Service: http://localhost:5000
echo User Service: http://localhost:5001
echo Student Service: http://localhost:5002
echo Course Service: http://localhost:5003
echo Parent Service: http://localhost:5004
