/**
 * Dashboard Router Component
 *
 * This component routes to the appropriate dashboard based on user role.
 *
 * English: This component shows the correct dashboard based on user role
 * Tanglish: Indha component user role-a based panni correct dashboard-a display pannum
 */

import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import SuperAdminDashboard from '../home/<USER>';
import AdminDashboard from '../home/<USER>';
import TeacherDashboard from '../home/<USER>';
import StudentDashboard from '../home/<USER>';
import ParentDashboard from '../home/<USER>';

const DashboardRouter = () => {
  const { currentUser } = useAuth();
  const navigate = useNavigate();

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!currentUser) {
      navigate('/login');
    }
  }, [currentUser, navigate]);

  if (!currentUser) {
    return null;
  }

  // Render dashboard based on user role
  switch (currentUser.role) {
    case 'Super Admin':
      return <SuperAdminDashboard />;

    case 'Admin':
      return <AdminDashboard />;

    case 'Teacher':
      return <TeacherDashboard />;

    case 'Student':
      return <StudentDashboard />;

    case 'Parent':
      return <ParentDashboard />;

    default:
      return (
        <div className="container mx-auto p-4">
          <div className="card">
            <h1 className="text-2xl font-bold mb-4">Unknown Role</h1>
            <p>Your role ({currentUser.role}) is not recognized.</p>
          </div>
        </div>
      );
  }
};

export default DashboardRouter;
