"""
Simple script to test authentication and user registration.

This script:
1. Tests the health endpoints of the Auth and User services
2. Logs in as Super Admin
3. Registers a new teacher
"""

import requests
import json
import time

print("Testing authentication and user registration...")

# 1. Test health endpoints
print("\nTesting health endpoints:")
auth_health_url = "http://localhost:5000/api/auth/health"
user_health_url = "http://localhost:5001/api/users/health"

try:
    auth_health_response = requests.get(auth_health_url)
    print(f"Auth Service health: {auth_health_response.status_code} - {auth_health_response.text}")
except Exception as e:
    print(f"Error checking Auth Service health: {str(e)}")

try:
    user_health_response = requests.get(user_health_url)
    print(f"User Service health: {user_health_response.status_code} - {user_health_response.text}")
except Exception as e:
    print(f"Error checking User Service health: {str(e)}")

# 2. Login as Super Admin
print("\nLogging in as Super Admin...")
login_url = "http://localhost:5000/api/auth/login"
login_data = {
    "username": "superadmin",
    "password": "password123"
}

try:
    login_response = requests.post(login_url, json=login_data)
    print(f"Login response status: {login_response.status_code}")
    print(f"Login response: {login_response.text}")
    
    if login_response.status_code == 200:
        login_result = login_response.json()
        token = login_result.get("token")
        print(f"\nToken: {token}")
        
        # 3. Register a new teacher
        print("\nRegistering a new teacher...")
        register_url = "http://localhost:5001/api/users/register"
        register_data = {
            "username": "teacher2",
            "password": "password123",
            "email": "<EMAIL>",
            "role": "Teacher"
        }
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        try:
            register_response = requests.post(register_url, json=register_data, headers=headers)
            print(f"Register response status: {register_response.status_code}")
            print(f"Register response: {register_response.text}")
        except Exception as e:
            print(f"Error registering teacher: {str(e)}")
except Exception as e:
    print(f"Error logging in: {str(e)}")

print("\nTest completed.")
