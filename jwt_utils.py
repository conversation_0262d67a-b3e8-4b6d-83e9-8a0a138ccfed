"""
JWT Utility functions.

This module provides utility functions for JWT token generation and verification.

English: This file contains functions for JWT token generation and verification
Tanglish: Indha file JWT token generate panradhukkum verify panradhukkum functions irukku
"""

import os
import jwt
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get JWT secret from environment variables
JWT_SECRET = os.environ.get('JWT_SECRET', 'simple-jwt-secret-for-school-management-system')

def generate_token(user_data, expiration_hours=24):
    """
    Generate a JWT token.

    Args:
        user_data: Dictionary containing user information (id, username, role, is_admin)
        expiration_hours: Token expiration time in hours (default: 24)

    Returns:
        JWT token string

    English: This function creates a JWT token with user information
    Tanglish: Indha function user information-oda JWT token-a create pannum
    """
    # Set token expiration time
    expiration = datetime.utcnow() + timedelta(hours=expiration_hours)

    # Create token payload
    payload = {
        'sub': str(user_data.get('id')),  # Convert user ID to string
        'username': user_data.get('username'),
        'role': user_data.get('role'),
        'is_admin': user_data.get('is_admin', False),
        'exp': expiration
    }

    # Generate token
    token = jwt.encode(payload, JWT_SECRET, algorithm='HS256')

    return token

def verify_token(token):
    """
    Verify a JWT token.

    Args:
        token: JWT token string

    Returns:
        Dictionary containing token payload if valid, None if invalid

    English: This function checks if a token is valid and returns the payload
    Tanglish: Indha function token valid-a irukka nu check panni, valid-na payload-a return pannum
    """
    try:
        # Decode and verify token
        payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return payload
    except (jwt.ExpiredSignatureError, jwt.InvalidTokenError):
        return None

def get_jwt_secret():
    """
    Get the JWT secret.

    Returns:
        JWT secret string

    English: This function returns the JWT secret
    Tanglish: Indha function JWT secret-a return pannum
    """
    return JWT_SECRET
