"""
Script to test teacher login and check if main_code is returned.
"""

import requests
import json

def test_teacher_login():
    """Test teacher login and check main_code."""
    print("Testing teacher login...")
    
    # Login as teacher
    login_url = "http://localhost:5000/api/auth/login"
    login_data = {
        "username": "teacher1",
        "password": "password123"
    }
    
    try:
        login_response = requests.post(login_url, json=login_data)
        print(f"Login response status: {login_response.status_code}")
        print(f"Login response: {json.dumps(login_response.json(), indent=2)}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            user_data = login_result.get("user", {})
            main_code = user_data.get("main_code")
            
            print(f"\nUser data received:")
            print(f"- ID: {user_data.get('id')}")
            print(f"- Username: {user_data.get('username')}")
            print(f"- Role: {user_data.get('role')}")
            print(f"- Main Code: {main_code}")
            
            if main_code:
                print(f"\n✅ SUCCESS: main_code '{main_code}' is present in login response!")
            else:
                print(f"\n❌ FAILED: main_code is missing from login response!")
                
        else:
            print(f"\n❌ Login failed with status {login_response.status_code}")
            
    except Exception as e:
        print(f"Error during login test: {str(e)}")

if __name__ == '__main__':
    test_teacher_login()
