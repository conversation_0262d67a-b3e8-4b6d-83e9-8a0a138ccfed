"""
Script to update JWT secret in each service's middleware.py file.

This script ensures all services use the same JWT secret from environment variables.
"""

import os
import re
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get JWT secret from environment variables
JWT_SECRET = os.environ.get('JWT_SECRET', 'simple-jwt-secret-for-school-management-system')

# List of service directories
services = ['auth_service', 'user_service', 'student_service', 'course_service', 'parent_service']

print("Starting to update JWT secret in middleware files...")

for service in services:
    middleware_path = os.path.join(service, 'common', 'middleware.py')

    if os.path.exists(middleware_path):
        print(f"Updating JWT secret in {middleware_path}...")

        with open(middleware_path, 'r') as f:
            content = f.read()

        # Check if dotenv is imported
        if 'from dotenv import load_dotenv' not in content:
            # Add dotenv import
            content = content.replace(
                'from werkzeug.wrappers import Request, Response',
                'from werkzeug.wrappers import Request, Response\nfrom dotenv import load_dotenv'
            )

            # Add load_dotenv() call
            content = content.replace(
                '# Configure logging',
                '# Load environment variables\nload_dotenv()\n\n# Configure logging'
            )

        # Replace the JWT secret initialization
        content = re.sub(
            r'self\.jwt_secret\s*=\s*[\'"].*[\'"]',
            f'self.jwt_secret = os.environ.get(\'JWT_SECRET\', \'{JWT_SECRET}\')',
            content
        )

        # Update the file
        with open(middleware_path, 'w') as f:
            f.write(content)

        print(f"Updated JWT secret in {middleware_path}")
    else:
        print(f"Could not find {middleware_path}")

print('\nJWT secrets updated in all middleware files.')
print('Please restart all services to use the new JWT secret.')
