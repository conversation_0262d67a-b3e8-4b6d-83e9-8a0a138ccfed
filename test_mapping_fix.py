#!/usr/bin/env python3
"""
Test script to verify the parent-student mapping fix
"""

import requests
import json
from datetime import datetime

# Base URLs
BASE_URLS = {
    'auth': 'http://localhost:5000/api/auth',
    'users': 'http://localhost:5001/api/users', 
    'students': 'http://localhost:5002/api/students',
    'parents': 'http://localhost:5004/api/parents'
}

def get_auth_token():
    """Get authentication token"""
    login_data = {
        "username": "superadmin",
        "password": "D@qwertyuiop"
    }
    
    response = requests.post(f"{BASE_URLS['auth']}/login", json=login_data)
    if response.status_code == 200:
        return response.json()['token']
    else:
        print(f"Login failed: {response.text}")
        return None

def create_test_user_and_student(token):
    """Create a test user and student"""
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # Create user
    user_data = {
        "username": f"test_student_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "email": f"test_student_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
        "password": "TestPass123!",
        "role": "Student"
    }
    
    user_response = requests.post(f"{BASE_URLS['users']}/register", json=user_data, headers=headers)
    if user_response.status_code != 201:
        print(f"User creation failed: {user_response.text}")
        return None, None
    
    user_id = user_response.json()['user']['id']
    print(f"✅ Created user with ID: {user_id}")
    
    # Create student
    student_data = {
        "user_id": user_id,
        "first_name": "Test",
        "last_name": "Student",
        "date_of_birth": "2005-01-15",
        "address": "123 Test Street",
        "phone": "1234567890"
    }
    
    student_response = requests.post(f"{BASE_URLS['students']}/students", json=student_data, headers=headers)
    if student_response.status_code != 201:
        print(f"Student creation failed: {student_response.text}")
        return user_id, None
    
    student_id = student_response.json()['student']['id']
    print(f"✅ Created student with ID: {student_id}")
    
    return user_id, student_id

def create_test_user_and_parent(token):
    """Create a test user and parent"""
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    # Create user
    user_data = {
        "username": f"test_parent_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "email": f"test_parent_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
        "password": "TestPass123!",
        "role": "Parent"
    }
    
    user_response = requests.post(f"{BASE_URLS['users']}/register", json=user_data, headers=headers)
    if user_response.status_code != 201:
        print(f"User creation failed: {user_response.text}")
        return None, None
    
    user_id = user_response.json()['user']['id']
    print(f"✅ Created user with ID: {user_id}")
    
    # Create parent
    parent_data = {
        "user_id": user_id,
        "first_name": "Test",
        "last_name": "Parent",
        "occupation": "Engineer",
        "address": "456 Test Avenue",
        "phone": "9876543210"
    }
    
    parent_response = requests.post(f"{BASE_URLS['parents']}/parents", json=parent_data, headers=headers)
    if parent_response.status_code != 201:
        print(f"Parent creation failed: {parent_response.text}")
        return user_id, None
    
    parent_id = parent_response.json()['parent']['id']
    print(f"✅ Created parent with ID: {parent_id}")
    
    return user_id, parent_id

def test_parent_student_mapping(token, student_id, parent_id):
    """Test parent-student mapping"""
    headers = {'Authorization': f'Bearer {token}', 'Content-Type': 'application/json'}
    
    mapping_data = {
        "student_id": student_id,
        "parent_id": parent_id,
        "relationship": "Father"
    }
    
    print(f"\n🔗 Testing parent-student mapping...")
    print(f"Student ID: {student_id}, Parent ID: {parent_id}")
    
    response = requests.post(f"{BASE_URLS['students']}/map-parent", json=mapping_data, headers=headers)
    
    print(f"Response Status: {response.status_code}")
    print(f"Response Content: {response.text}")
    
    if response.status_code == 201:
        print("✅ Parent-student mapping successful!")
        return True
    else:
        print("❌ Parent-student mapping failed!")
        return False

def main():
    """Main test function"""
    print("🧪 Testing Parent-Student Mapping Fix")
    print("="*50)
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        return
    
    print(f"✅ Got authentication token")
    
    # Create test student
    student_user_id, student_id = create_test_user_and_student(token)
    if not student_id:
        print("❌ Failed to create test student")
        return
    
    # Create test parent
    parent_user_id, parent_id = create_test_user_and_parent(token)
    if not parent_id:
        print("❌ Failed to create test parent")
        return
    
    # Test the mapping
    success = test_parent_student_mapping(token, student_id, parent_id)
    
    if success:
        print("\n🎉 All tests passed! The fix is working correctly.")
    else:
        print("\n💥 Test failed! There may still be issues.")

if __name__ == "__main__":
    main()
