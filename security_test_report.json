[{"timestamp": "2025-05-29T11:49:01.999444", "test_name": "SQL Injection - Login", "endpoint": "http://localhost:5000/api/auth/login", "method": "POST", "status_code": 401, "response": "{\n  \"error\": \"Invalid username or password\"\n}\n", "vulnerability_found": false, "details": "Payload: '; DROP TABLE users; --", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:04.227986", "test_name": "SQL Injection - Login", "endpoint": "http://localhost:5000/api/auth/login", "method": "POST", "status_code": 401, "response": "{\n  \"error\": \"Invalid username or password\"\n}\n", "vulnerability_found": false, "details": "Payload: ' OR '1'='1", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:06.279582", "test_name": "SQL Injection - Login", "endpoint": "http://localhost:5000/api/auth/login", "method": "POST", "status_code": 401, "response": "{\n  \"error\": \"Invalid username or password\"\n}\n", "vulnerability_found": false, "details": "Payload: ' UNION SELECT * FROM users --", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:08.310505", "test_name": "SQL Injection - Login", "endpoint": "http://localhost:5000/api/auth/login", "method": "POST", "status_code": 401, "response": "{\n  \"error\": \"Invalid username or password\"\n}\n", "vulnerability_found": false, "details": "Payload: admin'--", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:10.345460", "test_name": "SQL Injection - Login", "endpoint": "http://localhost:5000/api/auth/login", "method": "POST", "status_code": 401, "response": "{\n  \"error\": \"Invalid username or password\"\n}\n", "vulnerability_found": false, "details": "Payload: ' OR 1=1 --", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:12.371907", "test_name": "SQL Injection - User Registration", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 400, "response": "{\n  \"error\": \"Invalid input: Invalid username. Must be 3-50 characters, alphanumeric, underscore, or", "vulnerability_found": false, "details": "Payload: '; DROP TABLE users; --", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:14.404102", "test_name": "SQL Injection - User Registration", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 400, "response": "{\n  \"error\": \"Invalid input: Invalid username. Must be 3-50 characters, alphanumeric, underscore, or", "vulnerability_found": false, "details": "Payload: ' OR '1'='1", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:16.433047", "test_name": "XSS - User Registration (username)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 400, "response": "{\n  \"error\": \"Invalid input: Invalid username. Must be 3-50 characters, alphanumeric, underscore, or", "vulnerability_found": false, "details": "Payload in username: <script>alert('XSS')</script>", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:18.454688", "test_name": "XSS - User Registration (email)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 400, "response": "{\n  \"error\": \"Invalid input: Invalid email address format\"\n}\n", "vulnerability_found": false, "details": "Payload in email: <script>alert('XSS')</script>", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:20.495951", "test_name": "XSS - User Registration (username)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 400, "response": "{\n  \"error\": \"Invalid input: Invalid username. Must be 3-50 characters, alphanumeric, underscore, or", "vulnerability_found": false, "details": "Payload in username: javascript:alert('XSS')", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:22.510377", "test_name": "XSS - User Registration (email)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 400, "response": "{\n  \"error\": \"Invalid input: Invalid email address format\"\n}\n", "vulnerability_found": false, "details": "Payload in email: javascript:alert('XSS')", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:24.542479", "test_name": "XSS - User Registration (username)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 400, "response": "{\n  \"error\": \"Invalid input: Invalid username. Must be 3-50 characters, alphanumeric, underscore, or", "vulnerability_found": false, "details": "Payload in username: <img src=x onerror=alert('XSS')>", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:26.559423", "test_name": "XSS - User Registration (email)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 400, "response": "{\n  \"error\": \"Invalid input: Invalid email address format\"\n}\n", "vulnerability_found": false, "details": "Payload in email: <img src=x onerror=alert('XSS')>", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:28.578115", "test_name": "XSS - User Registration (username)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 400, "response": "{\n  \"error\": \"Invalid input: Invalid username. Must be 3-50 characters, alphanumeric, underscore, or", "vulnerability_found": false, "details": "Payload in username: ';alert('XSS');//", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:30.606524", "test_name": "XSS - User Registration (email)", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 400, "response": "{\n  \"error\": \"Invalid input: Invalid email address format\"\n}\n", "vulnerability_found": false, "details": "Payload in email: ';alert('XSS');//", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:32.632937", "test_name": "Authentication Bypass", "endpoint": "http://localhost:5001/api/users/users", "method": "GET", "status_code": 401, "response": "{\"error\": \"Missing or invalid token\"}", "vulnerability_found": false, "details": "Attempted access without authentication token", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:34.665284", "test_name": "Authentication Bypass", "endpoint": "http://localhost:5002/api/students/students", "method": "GET", "status_code": 401, "response": "{\"error\": \"Missing or invalid token\"}", "vulnerability_found": false, "details": "Attempted access without authentication token", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:36.710361", "test_name": "Authentication Bypass", "endpoint": "http://localhost:5004/api/parents/parents", "method": "GET", "status_code": 401, "response": "{\"error\": \"Missing or invalid token\"}", "vulnerability_found": false, "details": "Attempted access without authentication token", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:38.742471", "test_name": "Authentication Bypass", "endpoint": "http://localhost:5003/api/courses/courses", "method": "POST", "status_code": 401, "response": "{\"error\": \"Missing or invalid token\"}", "vulnerability_found": false, "details": "Attempted access without authentication token", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:40.772060", "test_name": "Invalid <PERSON>", "endpoint": "http://localhost:5001/api/users/users", "method": "GET", "status_code": 401, "response": "{\"error\": \"Invalid token: Not enough segments\"}", "vulnerability_found": false, "details": "Attempted access with invalid token", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:49:57.656399", "test_name": "Data Integrity - First Parent Mapping", "endpoint": "http://localhost:5002/api/students/map-parent", "method": "POST", "status_code": 201, "response": "{\n  \"mapping\": {\n    \"created_at\": \"2025-05-29T06:19:57.648409\",\n    \"id\": 19,\n    \"parent_id\": 15,\n", "vulnerability_found": false, "details": "First parent mapping should succeed", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:50:01.742270", "test_name": "Data Integrity - Multiple Parents Constraint", "endpoint": "http://localhost:5002/api/students/map-parent", "method": "POST", "status_code": 400, "response": "{\n  \"error\": \"Student already has a parent mapped. Only one parent per student is allowed.\"\n}\n", "vulnerability_found": false, "details": "Second parent mapping should be rejected (one parent per student rule)", "security_status": "SECURE"}, {"timestamp": "2025-05-29T11:50:03.794600", "test_name": "CORS Configuration", "endpoint": "http://localhost:5000/api/auth/login", "method": "OPTIONS", "status_code": 200, "response": "{'Server': 'Werkzeug/3.0.4 Python/3.13.2', 'Date': 'Thu, 29 May 2025 06:20:03 GMT', 'Content-Type': 'text/html; charset=utf-8', 'Allow': 'POST, OPTIONS', 'Content-Length': '0', 'Connection': 'close'}", "vulnerability_found": false, "details": "Access-Control-Allow-Origin: ", "security_status": "SECURE"}]