"""
<PERSON><PERSON>t to create a Super Admin user.

This script creates a Super Admin user directly in the auth_db database.
It should be run once during initial setup.

Usage:
    python create_super_admin.py --username superadmin --password yourpassword --email <EMAIL>
"""

import os
import sys
import argparse
import bcrypt
from datetime import datetime
from sqlalchemy import create_engine, Column, Integer, String, Boolean, DateTime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Define the User model
Base = declarative_base()

class User(Base):
    """User model for authentication."""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False)
    password = Column(String(255), nullable=False)
    email = Column(String(100), unique=True, nullable=False)
    role = Column(String(20), nullable=False)  # Super Admin, Admin, Teacher, Student, Parent
    is_admin = Column(Boolean, default=False)  # For Teachers who are also Admins
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)

def get_db_url():
    """Get the database URL for the auth service."""
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'postgres')
    db_host = os.environ.get('DB_HOST', 'localhost')
    db_port = os.environ.get('DB_PORT', '5432')
    db_name = 'auth_db'
    
    return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

def hash_password(password):
    """Hash a password using bcrypt."""
    # Convert password to bytes if it's a string
    if isinstance(password, str):
        password = password.encode('utf-8')

    # Generate a salt and hash the password
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password, salt)

    # Return the hashed password as a string
    return hashed.decode('utf-8')

def create_super_admin(username, password, email):
    """Create a Super Admin user."""
    # Get database URL
    db_url = get_db_url()
    
    # Create engine and session
    engine = create_engine(db_url)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Check if user already exists
        existing_user = session.query(User).filter(
            (User.username == username) | (User.email == email)
        ).first()
        
        if existing_user:
            print(f"Error: User with username '{username}' or email '{email}' already exists.")
            return False
        
        # Create tables if they don't exist
        Base.metadata.create_all(engine)
        
        # Create Super Admin user
        super_admin = User(
            username=username,
            password=hash_password(password),
            email=email,
            role='Super Admin',
            is_admin=True
        )
        
        # Add user to database
        session.add(super_admin)
        session.commit()
        
        print(f"Super Admin user '{username}' created successfully!")
        return True
        
    except Exception as e:
        print(f"Error creating Super Admin user: {e}")
        session.rollback()
        return False
        
    finally:
        session.close()

def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Create a Super Admin user.')
    parser.add_argument('--username', required=True, help='Super Admin username')
    parser.add_argument('--password', required=True, help='Super Admin password')
    parser.add_argument('--email', required=True, help='Super Admin email')
    
    args = parser.parse_args()
    
    # Create Super Admin user
    success = create_super_admin(args.username, args.password, args.email)
    
    # Exit with appropriate status code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
