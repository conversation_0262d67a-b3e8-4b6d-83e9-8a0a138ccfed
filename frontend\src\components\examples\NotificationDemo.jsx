/**
 * Notification Demo Component
 *
 * A demo component to test the new notification system.
 * Shows how to use success and error notifications with 5-second auto-dismiss.
 */

import { useNotification } from '../../contexts/NotificationContext';
import Button from '../field/Button';

const NotificationDemo = () => {
  const { showSuccess, showError, clearAllNotifications } = useNotification();

  const handleSuccessClick = () => {
    showSuccess('This is a success notification! It will auto-dismiss in 5 seconds.');
  };

  const handleErrorClick = () => {
    showError('This is an error notification! It should be hidden from users and only logged to console.');
  };

  const handleMultipleNotifications = () => {
    showSuccess('First success message');
    setTimeout(() => showError('Second error message (hidden)'), 500);
    setTimeout(() => showSuccess('Third success message'), 1000);
  };

  const handleLongMessage = () => {
    showSuccess('This is a very long success message to test how the notification system handles longer text content. It should wrap properly and still look good.');
  };

  const handleClearAll = () => {
    clearAllNotifications();
  };

  return (
    <div className="max-w-2xl mx-auto p-8 space-y-6">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">Notification System Demo</h2>
        
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-700 mb-2">Basic Notifications</h3>
            <div className="flex gap-4">
              <Button
                variant="success"
                onClick={handleSuccessClick}
                userRole="super_admin"
              >
                Show Success
              </Button>
              
              <Button
                variant="danger"
                onClick={handleErrorClick}
                userRole="super_admin"
              >
                Test Error (Hidden)
              </Button>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-700 mb-2">Multiple Notifications</h3>
            <Button
              variant="primary"
              onClick={handleMultipleNotifications}
              userRole="super_admin"
            >
              Show Multiple (Stacked)
            </Button>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-700 mb-2">Long Message</h3>
            <Button
              variant="info"
              onClick={handleLongMessage}
              userRole="super_admin"
            >
              Show Long Message
            </Button>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-700 mb-2">Clear All</h3>
            <Button
              variant="secondary"
              onClick={handleClearAll}
              userRole="super_admin"
            >
              Clear All Notifications
            </Button>
          </div>
        </div>

        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold text-gray-700 mb-2">Features:</h4>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>✅ Green color for all success messages</li>
            <li>✅ Error messages are hidden from users (logged to console only)</li>
            <li>✅ Popups display in center of screen</li>
            <li>✅ 5-second auto-dismiss functionality</li>
            <li>✅ Manual close button</li>
            <li>✅ Progress bar showing countdown</li>
            <li>✅ Smooth animations with scale effect</li>
            <li>✅ Multiple notifications stack properly</li>
            <li>✅ Responsive design</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default NotificationDemo;
