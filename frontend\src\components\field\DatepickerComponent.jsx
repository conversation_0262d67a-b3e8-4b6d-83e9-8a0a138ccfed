/**
 * DatepickerComponent
 *
 * A reusable date picker component with role-specific styling and validation.
 * Supports role-specific styling for different user panels (super_admin, admin, teacher, student, parent).
 */

import { useState } from 'react';

const DatepickerComponent = ({
  id,
  name,
  label,
  value,
  onChange,
  placeholder = '',
  error = '',
  required = false,
  disabled = false,
  className = '',
  min,
  max,
  userRole = '', // super_admin, admin, teacher, student, parent
  showIcon = true,
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  // Role-specific color schemes
  const getRoleColors = () => {
    switch (userRole) {
      case 'super_admin':
        return {
          focusRing: 'focus:ring-purple-500',
          focusBorder: 'focus:border-purple-500',
          bgColor: 'bg-purple-50',
          labelColor: 'text-purple-700',
          iconColor: 'text-purple-500'
        };
      case 'admin':
        return {
          focusRing: 'focus:ring-green-500',
          focusBorder: 'focus:border-green-500',
          bgColor: 'bg-green-50',
          labelColor: 'text-green-700',
          iconColor: 'text-green-500'
        };
      case 'teacher':
        return {
          focusRing: 'focus:ring-indigo-500',
          focusBorder: 'focus:border-indigo-500',
          bgColor: 'bg-indigo-50',
          labelColor: 'text-indigo-700',
          iconColor: 'text-indigo-500'
        };
      case 'student':
        return {
          focusRing: 'focus:ring-blue-500',
          focusBorder: 'focus:border-blue-500',
          bgColor: 'bg-blue-50',
          labelColor: 'text-blue-700',
          iconColor: 'text-blue-500'
        };
      case 'parent':
        return {
          focusRing: 'focus:ring-orange-500',
          focusBorder: 'focus:border-orange-500',
          bgColor: 'bg-orange-50',
          labelColor: 'text-orange-700',
          iconColor: 'text-orange-500'
        };
      default:
        return {
          focusRing: 'focus:ring-blue-500',
          focusBorder: 'focus:border-blue-500',
          bgColor: 'bg-gray-50',
          labelColor: 'text-gray-700',
          iconColor: 'text-gray-500'
        };
    }
  };

  const { focusRing, focusBorder, bgColor, labelColor, iconColor } = getRoleColors();

  return (
    <div className={`mb-4 ${className}`}>
      {label && (
        <label
          htmlFor={id || name}
          className={`block text-sm font-medium mb-2 ${labelColor}`}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      <div className="relative">
        <input
          id={id || name}
          name={name}
          type="date"
          value={value}
          onChange={onChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          min={min}
          max={max}
          className={`
            w-full px-3 py-2 border rounded-md shadow-sm
            focus:outline-none focus:ring-2 ${focusRing}
            ${disabled ? 'bg-gray-100 cursor-not-allowed' : bgColor}
            ${error ? 'border-red-500' : 'border-gray-300'}
            ${isFocused ? focusBorder : ''}
            ${showIcon ? 'pr-10' : ''}
          `}
        />

        {showIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <svg
              className={`h-5 w-5 ${iconColor}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
          </div>
        )}
      </div>

      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};

export default DatepickerComponent;
