import axios from "axios";
import config from "../../config";
import { authService } from "../authService";

export const parentService = {
  // API Service Methods
  parentRegisterService: (data) => {
    return axios.post(`${config.API.PARENTS}/parents`, data);
  },
  parentGetAllService: () => {
    return axios.get(`${config.API.PARENTS}/parents`);
  },
  parentGetByIdService: (parentId) => {
    return axios.get(`${config.API.PARENTS}/parents/${parentId}`);
  },
  parentGetByUserIdService: (userId) => {
    return axios.get(`${config.API.PARENTS}/parents/user/${userId}`);
  },

  // Utility Methods (for backward compatibility)
  registerParent: async (parentData) => {
    try {
      const response = await axios.post(`${config.API.PARENTS}/parents`, parentData, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getParents: async () => {
    try {
      const response = await axios.get(`${config.API.PARENTS}/parents`, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getParent: async (parentId) => {
    try {
      const response = await axios.get(`${config.API.PARENTS}/parents/${parentId}`, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },

  getParentByUserId: async (userId) => {
    try {
      const response = await axios.get(`${config.API.PARENTS}/parents/user/${userId}`, {
        headers: authService.getAuthHeader()
      });
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : { error: 'Network error' };
    }
  },
};
