"""
Views for the Parent Service.

This module defines the API endpoints for the Parent Service.

English: This file defines the API endpoints for the Parent Service
Tanglish: Indha file Parent Service-kku API endpoints-a define pannum
"""

from flask import Blueprint, jsonify
from parent_service.controllers import (
    register_parent, get_parents, get_parent, get_parent_by_user_id
)

# Create a Blueprint for parent routes
parent_bp = Blueprint('parent', __name__)

# Add route handlers for OPTIONS requests
@parent_bp.route('/<path:path>', methods=['OPTIONS'])
def handle_options_path(path):
    """
    Handle OPTIONS requests for CORS preflight with path.

    Returns:
        Empty response with appropriate CORS headers

    English: This endpoint handles OPTIONS requests for CORS
    Tanglish: Indha endpoint CORS-kku OPTIONS requests-a handle pannum
    """
    return "", 200

@parent_bp.route('/', methods=['OPTIONS'])
def handle_options_root():
    """
    Handle OPTIONS requests for CORS preflight at root.

    Returns:
        Empty response with appropriate CORS headers

    English: This endpoint handles OPTIONS requests for CORS at root
    Tanglish: Indha endpoint CORS-kku OPTIONS requests-a root-la handle pannum
    """
    return "", 200

@parent_bp.route('/parents', methods=['POST'])
def register_parent_route():
    """
    Register parent endpoint.

    Returns:
        Response from register_parent controller

    English: This endpoint registers a new parent
    Tanglish: Indha endpoint puthusa oru parent-a register pannum
    """
    return register_parent()

@parent_bp.route('/parents', methods=['GET'])
def get_parents_route():
    """
    Get all parents endpoint.

    Returns:
        Response from get_parents controller

    English: This endpoint gets all parents
    Tanglish: Indha endpoint ella parents-um get pannum
    """
    return get_parents()

@parent_bp.route('/parents/<int:parent_id>', methods=['GET'])
def get_parent_route(parent_id):
    """
    Get a specific parent endpoint.

    Args:
        parent_id: ID of the parent to get

    Returns:
        Response from get_parent controller

    English: This endpoint gets a specific parent
    Tanglish: Indha endpoint specific parent-a get pannum
    """
    return get_parent(parent_id)

@parent_bp.route('/parents/user/<int:user_id>', methods=['GET'])
def get_parent_by_user_id_route(user_id):
    """
    Get a parent by user ID endpoint.

    Args:
        user_id: ID of the user

    Returns:
        Response from get_parent_by_user_id controller

    English: This endpoint gets a parent by user ID
    Tanglish: Indha endpoint user ID moolama parent-a get pannum
    """
    return get_parent_by_user_id(user_id)

@parent_bp.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint.

    Returns:
        JSON response indicating the service is running

    English: This endpoint checks if the service is running
    Tanglish: Indha endpoint service odi kondu irukka nu check pannum
    """
    return jsonify({"status": "healthy", "service": "parent"})
