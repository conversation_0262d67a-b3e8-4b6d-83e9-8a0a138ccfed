"""
Logging configuration for microservices.

This module provides a standardized logging setup for all microservices.

English: This file sets up logging for all services
Tanglish: Indha file ella services-kkum logging setup pannum
"""

import os
import logging
from logging.handlers import RotatingFileHandler

def setup_logger(service_name):
    """
    Set up a logger for a specific service.
    
    Args:
        service_name: Name of the service
        
    Returns:
        Configured logger instance
        
    English: This function sets up a logger for a specific service
    Tanglish: Indha function specific service-kku logger-a setup pannum
    """
    # Create logs directory if it doesn't exist
    if not os.path.exists('logs'):
        os.makedirs('logs')
        
    # Create a logger
    logger = logging.getLogger(service_name)
    logger.setLevel(logging.INFO)
    
    # Create a file handler that logs even debug messages
    log_file = f"logs/{service_name}.log"
    file_handler = RotatingFileHandler(log_file, maxBytes=10485760, backupCount=5)
    file_handler.setLevel(logging.INFO)
    
    # Create a console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Create a formatter and add it to the handlers
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Add the handlers to the logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger
