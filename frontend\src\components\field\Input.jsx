/**
 * Input Component
 *
 * A reusable input field component with label and error handling.
 * Supports role-specific styling for different user panels (super_admin, admin, teacher, students, parent).
 */

import { useState } from 'react';

const Input = ({
  id,
  name,
  type = 'text',
  label,
  value,
  onChange,
  placeholder = '',
  error = '',
  required = false,
  disabled = false,
  className = '',
  autoComplete = 'on',
  maxLength,
  minLength,
  pattern,
  userRole = '', // super_admin, admin, teacher, student, parent
}) => {
  const [isFocused, setIsFocused] = useState(false);

  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);

  // Role-specific focus ring and border colors
  const getRoleStyles = () => {
    switch(userRole) {
      case 'super_admin':
        return {
          focusRing: 'focus:ring-purple-500',
          focusBorder: 'border-purple-500',
          bgColor: 'bg-purple-50'
        };
      case 'admin':
        return {
          focusRing: 'focus:ring-green-500',
          focusBorder: 'border-green-500',
          bgColor: 'bg-green-50'
        };
      case 'teacher':
        return {
          focusRing: 'focus:ring-indigo-500',
          focusBorder: 'border-indigo-500',
          bgColor: 'bg-indigo-50'
        };
      case 'student':
        return {
          focusRing: 'focus:ring-blue-500',
          focusBorder: 'border-blue-500',
          bgColor: 'bg-blue-50'
        };
      case 'parent':
        return {
          focusRing: 'focus:ring-teal-500',
          focusBorder: 'border-teal-500',
          bgColor: 'bg-teal-50'
        };
      default:
        return {
          focusRing: 'focus:ring-blue-500',
          focusBorder: 'border-blue-500',
          bgColor: ''
        };
    }
  };

  const { focusRing, focusBorder, bgColor } = getRoleStyles();

  // Role-specific validation patterns
  const getValidationPattern = () => {
    // If a pattern is explicitly provided, use that
    if (pattern) return pattern;

    // Otherwise, provide role & field specific patterns
    if (name === 'email') {
      return '[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,}$';
    }

    if (name === 'phone') {
      return '[0-9]{10,}';
    }

    return null;
  };

  return (
    <div className={`mb-4 ${className}`}>
      {label && (
        <label
          htmlFor={id || name}
          className={`block text-sm font-medium text-gray-700 mb-1 ${userRole ? `text-${userRole}-700` : ''}`}
        >
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}

      <input
        id={id || name}
        name={name}
        type={type}
        value={value}
        onChange={onChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        placeholder={placeholder}
        disabled={disabled}
        required={required}
        autoComplete={autoComplete}
        maxLength={maxLength}
        minLength={minLength}
        pattern={getValidationPattern()}
        className={`
          w-full px-3 py-2 border rounded-md shadow-sm
          focus:outline-none focus:ring-2 ${focusRing}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : bgColor}
          ${error ? 'border-red-500' : 'border-gray-300'}
          ${isFocused ? focusBorder : ''}
        `}
      />

      {error && (
        <p className="mt-1 text-sm text-red-600">{error}</p>
      )}
    </div>
  );
};

export default Input;
