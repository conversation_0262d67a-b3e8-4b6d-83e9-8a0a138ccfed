/**
 * Protected Route Component
 *
 * This component protects routes that require authentication.
 * It redirects to the login page if the user is not authenticated.
 */

import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

const ProtectedRoute = ({ 
  children, 
  allowedRoles = [], // Array of roles allowed to access this route
  redirectPath = '/login' 
}) => {
  const { currentUser, isLoading } = useAuth();
  const location = useLocation();
  
  // Show loading state if auth context is still loading
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  // If user is not authenticated, redirect to login
  if (!currentUser) {
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }
  
  // If allowedRoles is provided and user's role is not in the allowed roles, redirect to dashboard
  if (allowedRoles.length > 0 && !allowedRoles.includes(currentUser.role)) {
    return <Navigate to="/dashboard" replace />;
  }
  
  // If user is authenticated and has the required role, render the protected component
  return children;
};

export default ProtectedRoute;
