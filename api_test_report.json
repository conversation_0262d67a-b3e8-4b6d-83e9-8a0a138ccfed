[{"timestamp": "2025-05-29T11:50:14.473009", "endpoint": "http://localhost:5000/api/auth/health", "method": "GET", "status_code": 200, "response": {"service": "auth", "status": "healthy"}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:18.728209", "endpoint": "http://localhost:5000/api/auth/login", "method": "POST", "status_code": 200, "response": {"token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************.X85U0e-VNZKepAopRmSy8GluwclJMYZCcezjkTD5w3A", "user": {"course": null, "created_at": "2025-05-14T11:32:23.476374", "email": "<EMAIL>", "id": 28, "is_admin": true, "main_code": "RVM677", "role": "Super Admin", "updated_at": "2025-05-14T11:32:23.476374", "username": "superadmin"}}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:20.762189", "endpoint": "http://localhost:5000/api/auth/verify", "method": "GET", "status_code": 200, "response": {"user": {"email": "<EMAIL>", "id": 28, "is_admin": true, "role": "Super Admin", "username": "superadmin"}, "valid": true}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:22.785648", "endpoint": "http://localhost:5001/api/users/health", "method": "GET", "status_code": 200, "response": {"service": "user", "status": "healthy"}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:24.832567", "endpoint": "http://localhost:5001/api/users/users", "method": "GET", "status_code": 200, "response": {"users": [{"course": null, "created_at": "2025-05-14T11:31:44.188771", "email": "<EMAIL>", "id": 27, "is_admin": true, "main_code": "CCI711", "role": "Super Admin", "updated_at": "2025-05-14T11:31:44.188771", "username": "superadmin3"}, {"course": null, "created_at": "2025-05-14T11:32:23.476374", "email": "<EMAIL>", "id": 28, "is_admin": true, "main_code": "RVM677", "role": "Super Admin", "updated_at": "2025-05-14T11:32:23.476374", "username": "superadmin"}, {"course": "<PERSON><PERSON><PERSON>", "created_at": "2025-05-14T11:58:44.175420", "email": "<EMAIL>", "id": 30, "is_admin": false, "main_code": "RVM677", "role": "Teacher", "updated_at": "2025-05-14T11:58:44.175462", "username": "kowsi"}, {"course": "<PERSON><PERSON><PERSON>", "created_at": "2025-05-14T13:15:10.320732", "email": "<EMAIL>", "id": 32, "is_admin": false, "main_code": "RVM677", "role": "Teacher", "updated_at": "2025-05-14T13:15:10.320739", "username": "mockteacher"}, {"course": null, "created_at": "2025-05-14T13:15:18.678141", "email": "<EMAIL>", "id": 33, "is_admin": false, "main_code": "RVM677", "role": "Student", "updated_at": "2025-05-14T13:15:18.678145", "username": "mockstudent"}, {"course": null, "created_at": "2025-05-14T13:19:44.202140", "email": "<EMAIL>", "id": 34, "is_admin": false, "main_code": "RVM677", "role": "Parent", "updated_at": "2025-05-14T13:19:44.202149", "username": "mockparent"}, {"course": null, "created_at": "2025-05-15T05:06:58.728848", "email": "<PERSON><PERSON><PERSON><PERSON><PERSON><EMAIL>", "id": 38, "is_admin": false, "main_code": "RVM677", "role": "Parent", "updated_at": "2025-05-15T05:06:58.728853", "username": "hari"}, {"course": null, "created_at": "2025-05-15T05:11:20.704195", "email": "<EMAIL>", "id": 39, "is_admin": false, "main_code": "", "role": "Student", "updated_at": "2025-05-15T05:11:20.704205", "username": "selva"}, {"course": null, "created_at": "2025-05-15T05:12:59.552028", "email": "<EMAIL>", "id": 40, "is_admin": false, "main_code": "", "role": "Parent", "updated_at": "2025-05-15T05:12:59.552034", "username": "a<PERSON>l"}, {"course": null, "created_at": "2025-05-15T05:14:22.726540", "email": "<EMAIL>", "id": 41, "is_admin": false, "main_code": "", "role": "Student", "updated_at": "2025-05-15T05:14:22.726547", "username": "deepak"}, {"course": null, "created_at": "2025-05-15T05:16:39.382316", "email": "<EMAIL>", "id": 42, "is_admin": false, "main_code": "", "role": "Parent", "updated_at": "2025-05-15T05:16:39.382351", "username": "<PERSON><PERSON>"}, {"course": null, "created_at": "2025-05-15T05:30:13.337903", "email": "<EMAIL>", "id": 43, "is_admin": false, "main_code": "RVM677", "role": "Admin", "updated_at": "2025-05-15T05:30:13.337912", "username": "rag<PERSON>"}, {"course": null, "created_at": "2025-05-15T07:58:03.005414", "email": "<EMAIL>", "id": 44, "is_admin": false, "main_code": "", "role": "Student", "updated_at": "2025-05-15T07:58:03.005424", "username": "james"}, {"course": null, "created_at": "2025-05-15T09:44:05.422145", "email": "<EMAIL>", "id": 48, "is_admin": false, "main_code": "RVM677", "role": "Student", "updated_at": "2025-05-15T09:44:05.422156", "username": "praveen"}, {"course": "<PERSON>e Advanced", "created_at": "2025-05-15T04:58:04.857993", "email": "<EMAIL>", "id": 36, "is_admin": false, "main_code": "RVM677", "role": "Teacher", "updated_at": "2025-05-15T04:58:04.858036", "username": "<PERSON><PERSON>"}, {"course": "<PERSON>e Advanced", "created_at": "2025-05-14T13:24:51.707170", "email": "<EMAIL>", "id": 35, "is_admin": false, "main_code": "RVM677", "role": "Teacher", "updated_at": "2025-05-14T13:24:51.707177", "username": "mockteacher2"}, {"course": "<PERSON><PERSON><PERSON>", "created_at": "2025-05-15T11:14:23.453351", "email": "<EMAIL>", "id": 50, "is_admin": false, "main_code": "RVM677", "role": "Teacher", "updated_at": "2025-05-15T11:14:23.453358", "username": "<PERSON><PERSON><PERSON><PERSON>"}, {"course": null, "created_at": "2025-05-15T11:21:09.715039", "email": "<EMAIL>", "id": 51, "is_admin": false, "main_code": "RVM677", "role": "Student", "updated_at": "2025-05-15T11:21:09.715047", "username": "charan"}, {"course": null, "created_at": "2025-05-29T05:53:27.426366", "email": "<EMAIL>", "id": 69, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T05:53:27.426371", "username": "student_user_20250529_112325"}, {"course": null, "created_at": "2025-05-15T05:05:52.099559", "email": "<EMAIL>", "id": 37, "is_admin": false, "main_code": "RVM677", "role": "Student", "updated_at": "2025-05-26T12:40:49.910849", "username": "<PERSON><PERSON><PERSON>"}, {"course": null, "created_at": "2025-05-27T10:06:29.427201", "email": "<EMAIL>", "id": 54, "is_admin": false, "main_code": "RVM677", "role": "Student", "updated_at": "2025-05-27T10:06:29.427213", "username": "ram"}, {"course": "<PERSON><PERSON><PERSON>", "created_at": "2025-05-27T10:10:49.507087", "email": "<EMAIL>", "id": 55, "is_admin": false, "main_code": "RVM677", "role": "Teacher", "updated_at": "2025-05-27T10:10:49.507095", "username": "Amrish1"}, {"course": null, "created_at": "2025-05-27T10:12:40.216857", "email": "<EMAIL>", "id": 56, "is_admin": false, "main_code": "RVM677", "role": "Student", "updated_at": "2025-05-27T10:12:40.216870", "username": "selva2"}, {"course": null, "created_at": "2025-05-27T10:13:26.437499", "email": "<EMAIL>", "id": 57, "is_admin": false, "main_code": "RVM677", "role": "Parent", "updated_at": "2025-05-27T10:13:26.437512", "username": "raghul2"}, {"course": null, "created_at": "2025-05-27T10:20:08.744408", "email": "<EMAIL>", "id": 58, "is_admin": false, "main_code": "RVM677", "role": "Parent", "updated_at": "2025-05-27T10:20:08.744416", "username": "Mallika1"}, {"course": null, "created_at": "2025-05-28T11:02:01.290337", "email": "<EMAIL>", "id": 62, "is_admin": false, "main_code": "KMW599", "role": "Teacher", "updated_at": "2025-05-28T11:02:01.290337", "username": "teacher1"}, {"course": null, "created_at": "2025-05-28T05:35:45.980570", "email": "<EMAIL>", "id": 63, "is_admin": false, "main_code": "KMW599", "role": "Student", "updated_at": "2025-05-28T05:35:45.980583", "username": "student_test1"}, {"course": "<PERSON>e", "created_at": "2025-05-28T05:39:47.156007", "email": "<EMAIL>", "id": 64, "is_admin": false, "main_code": "RVM677", "role": "Teacher", "updated_at": "2025-05-28T05:39:47.156042", "username": "<PERSON>uma<PERSON>"}, {"course": null, "created_at": "2025-05-28T05:43:03.875917", "email": "<EMAIL>", "id": 65, "is_admin": false, "main_code": "RVM677", "role": "Student", "updated_at": "2025-05-28T05:43:03.875948", "username": "selva5"}, {"course": null, "created_at": "2025-05-28T05:44:26.871536", "email": "<EMAIL>", "id": 66, "is_admin": false, "main_code": "RVM677", "role": "Student", "updated_at": "2025-05-28T05:44:26.871560", "username": "<PERSON><PERSON>"}, {"course": null, "created_at": "2025-05-29T05:50:42.772426", "email": "<EMAIL>", "id": 67, "is_admin": false, "main_code": null, "role": "Teacher", "updated_at": "2025-05-29T05:50:42.772433", "username": "testuser_20250529_112040"}, {"course": null, "created_at": "2025-05-29T05:53:19.109864", "email": "<EMAIL>", "id": 68, "is_admin": false, "main_code": null, "role": "Teacher", "updated_at": "2025-05-29T05:53:19.109869", "username": "testuser_20250529_112316"}, {"course": null, "created_at": "2025-05-29T05:53:37.758213", "email": "<EMAIL>", "id": 70, "is_admin": false, "main_code": null, "role": "Parent", "updated_at": "2025-05-29T05:53:37.758218", "username": "parent_user_20250529_112335"}, {"course": null, "created_at": "2025-05-29T05:56:53.076740", "email": "<EMAIL>", "id": 71, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T05:56:53.076747", "username": "test_student_20250529_112650"}, {"course": null, "created_at": "2025-05-29T05:56:57.381967", "email": "<EMAIL>", "id": 72, "is_admin": false, "main_code": null, "role": "Parent", "updated_at": "2025-05-29T05:56:57.381975", "username": "test_parent_20250529_112655"}, {"course": null, "created_at": "2025-05-29T05:57:24.248167", "email": "<EMAIL>", "id": 73, "is_admin": false, "main_code": null, "role": "Teacher", "updated_at": "2025-05-29T05:57:24.248172", "username": "testuser_20250529_112722"}, {"course": null, "created_at": "2025-05-29T05:57:32.557818", "email": "<EMAIL>", "id": 74, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T05:57:32.557823", "username": "student_user_20250529_112730"}, {"course": null, "created_at": "2025-05-29T05:57:42.888080", "email": "<EMAIL>", "id": 75, "is_admin": false, "main_code": null, "role": "Parent", "updated_at": "2025-05-29T05:57:42.888085", "username": "parent_user_20250529_112740"}, {"course": null, "created_at": "2025-05-29T05:59:33.552125", "email": "<EMAIL>", "id": 76, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T05:59:33.552130", "username": "'; DROP TABLE users; --"}, {"course": null, "created_at": "2025-05-29T05:59:35.790790", "email": "<EMAIL>", "id": 77, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T05:59:35.790795", "username": "' OR '1'='1"}, {"course": null, "created_at": "2025-05-29T05:59:38.031422", "email": "<EMAIL>", "id": 78, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T05:59:38.031427", "username": "<script>alert('XSS')</script>"}, {"course": null, "created_at": "2025-05-29T05:59:40.279469", "email": "<script>alert('XSS')</script>@example.com", "id": 79, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T05:59:40.279473", "username": "test_20250529_112935"}, {"course": null, "created_at": "2025-05-29T05:59:42.532429", "email": "<EMAIL>", "id": 80, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T05:59:42.532434", "username": "javascript:alert('XSS')"}, {"course": null, "created_at": "2025-05-29T05:59:44.761743", "email": "javascript:alert('XSS')@example.com", "id": 81, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T05:59:44.761748", "username": "test_20250529_112940"}, {"course": null, "created_at": "2025-05-29T05:59:47.011605", "email": "<EMAIL>", "id": 82, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T05:59:47.011609", "username": "<img src=x onerror=alert('XSS')>"}, {"course": null, "created_at": "2025-05-29T05:59:49.253740", "email": "<img src=x onerror=alert('XSS')>@example.com", "id": 83, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T05:59:49.253744", "username": "test_20250529_112944"}, {"course": null, "created_at": "2025-05-29T05:59:51.491028", "email": "<EMAIL>", "id": 84, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T05:59:51.491032", "username": "';alert('XSS');//"}, {"course": null, "created_at": "2025-05-29T05:59:53.734560", "email": "';alert('XSS');//@example.com", "id": 85, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T05:59:53.734565", "username": "test_20250529_112949"}, {"course": null, "created_at": "2025-05-29T06:00:06.123553", "email": "<EMAIL>", "id": 86, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T06:00:06.123558", "username": "integrity_student_20250529_113003"}, {"course": null, "created_at": "2025-05-29T06:00:10.402115", "email": "<EMAIL>", "id": 87, "is_admin": false, "main_code": null, "role": "Parent", "updated_at": "2025-05-29T06:00:10.402120", "username": "integrity_parent_0_20250529_113008"}, {"course": null, "created_at": "2025-05-29T06:00:14.684907", "email": "<EMAIL>", "id": 88, "is_admin": false, "main_code": null, "role": "Parent", "updated_at": "2025-05-29T06:00:14.684911", "username": "integrity_parent_1_20250529_113012"}, {"course": null, "created_at": "2025-05-29T06:19:42.991537", "email": "<EMAIL>", "id": 89, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T06:19:42.991544", "username": "integrity_student_20250529_114940"}, {"course": null, "created_at": "2025-05-29T06:19:47.281882", "email": "<EMAIL>", "id": 90, "is_admin": false, "main_code": null, "role": "Parent", "updated_at": "2025-05-29T06:19:47.281886", "username": "integrity_parent_0_20250529_114945"}, {"course": null, "created_at": "2025-05-29T06:19:51.565125", "email": "<EMAIL>", "id": 91, "is_admin": false, "main_code": null, "role": "Parent", "updated_at": "2025-05-29T06:19:51.565130", "username": "integrity_parent_1_20250529_114949"}]}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:27.051556", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 201, "response": {"message": "User registered successfully", "user": {"course": null, "created_at": "2025-05-29T06:20:27.048019", "email": "<EMAIL>", "id": 92, "is_admin": false, "main_code": null, "role": "Teacher", "updated_at": "2025-05-29T06:20:27.048024", "username": "testuser_20250529_115024"}}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:29.092921", "endpoint": "http://localhost:5001/api/users/users/92", "method": "GET", "status_code": 200, "response": {"user": {"course": null, "created_at": "2025-05-29T06:20:27.048019", "email": "<EMAIL>", "id": 92, "is_admin": false, "main_code": null, "role": "Teacher", "updated_at": "2025-05-29T06:20:27.048024", "username": "testuser_20250529_115024"}}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:31.120931", "endpoint": "http://localhost:5002/api/students/health", "method": "GET", "status_code": 200, "response": {"service": "student", "status": "healthy"}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:33.156855", "endpoint": "http://localhost:5002/api/students/students", "method": "GET", "status_code": 200, "response": {"students": [{"address": "123 Student St", "created_at": "2025-05-14T13:19:35.150796", "date_of_birth": "2000-01-01", "first_name": "<PERSON><PERSON>", "id": 4, "last_name": "Student", "phone": "**********", "updated_at": "2025-05-14T13:19:35.150803", "user_id": 33}, {"address": "new india colonoy", "created_at": "2025-05-15T05:05:52.120600", "date_of_birth": "2003-01-25", "first_name": "<PERSON><PERSON><PERSON>", "id": 5, "last_name": "AL", "phone": "**********", "updated_at": "2025-05-15T05:05:52.120620", "user_id": 37}, {"address": "new india colonoy", "created_at": "2025-05-15T05:11:21.027846", "date_of_birth": "2002-09-24", "first_name": "selva", "id": 6, "last_name": "<PERSON>uma<PERSON>", "phone": "**********", "updated_at": "2025-05-15T05:11:21.027851", "user_id": 39}, {"address": "new india colonoy", "created_at": "2025-05-15T05:14:22.737303", "date_of_birth": "2000-12-04", "first_name": "<PERSON><PERSON>", "id": 7, "last_name": "M", "phone": "**********", "updated_at": "2025-05-15T05:14:22.737307", "user_id": 41}, {"address": "new india colonoy", "created_at": "2025-05-15T07:58:03.640159", "date_of_birth": "1995-08-24", "first_name": "james", "id": 8, "last_name": "hems", "phone": "**********", "updated_at": "2025-05-15T07:58:03.640179", "user_id": 44}, {"address": "new india colonoy", "created_at": "2025-05-15T09:44:05.745338", "date_of_birth": "2001-05-05", "first_name": "praveen", "id": 11, "last_name": "<PERSON>uma<PERSON>", "phone": "**********", "updated_at": "2025-05-15T09:44:05.745342", "user_id": 48}, {"address": "new india colonoy", "created_at": "2025-05-15T11:21:10.095206", "date_of_birth": "2000-12-04", "first_name": "charan", "id": 12, "last_name": "dsais", "phone": "9344927687", "updated_at": "2025-05-15T11:21:10.095212", "user_id": 51}, {"address": "new india colonoy", "created_at": "2025-05-27T10:06:29.454310", "date_of_birth": "2005-04-07", "first_name": "ram", "id": 15, "last_name": "bro", "phone": "**********", "updated_at": "2025-05-27T10:06:29.454323", "user_id": 54}, {"address": "new india colonoy", "created_at": "2025-05-27T10:12:40.242729", "date_of_birth": "2004-07-29", "first_name": "selva", "id": 16, "last_name": "<PERSON>uma<PERSON>", "phone": "**********", "updated_at": "2025-05-27T10:12:40.242743", "user_id": 56}, {"address": "new india colonoy", "created_at": "2025-05-28T04:53:19.556809", "date_of_birth": "2006-02-08", "first_name": "a<PERSON>l", "id": 19, "last_name": "dsais", "phone": "**********", "updated_at": "2025-05-28T04:53:19.556816", "user_id": 60}, {"address": "new india colonoy", "created_at": "2025-05-28T05:06:44.572490", "date_of_birth": "2000-02-07", "first_name": "<PERSON>", "id": 20, "last_name": "dsais", "phone": "**********", "updated_at": "2025-05-28T05:06:44.572497", "user_id": 61}, {"address": "new india colonoy", "created_at": "2025-05-28T05:43:03.970041", "date_of_birth": "2006-09-28", "first_name": "selva", "id": 21, "last_name": "machi", "phone": "**********", "updated_at": "2025-05-28T05:43:03.970078", "user_id": 65}, {"address": "new india colonoy", "created_at": "2025-05-28T05:44:26.954561", "date_of_birth": "2007-05-20", "first_name": "malli", "id": 22, "last_name": "bro", "phone": "**********", "updated_at": "2025-05-28T05:44:26.954592", "user_id": 66}, {"address": "123 Test Street", "created_at": "2025-05-29T05:53:29.471896", "date_of_birth": "2005-01-15", "first_name": "Test", "id": 23, "last_name": "Student", "phone": "**********", "updated_at": "2025-05-29T05:53:29.471902", "user_id": 69}, {"address": "123 Test Street", "created_at": "2025-05-29T05:56:55.125396", "date_of_birth": "2005-01-15", "first_name": "Test", "id": 24, "last_name": "Student", "phone": "**********", "updated_at": "2025-05-29T05:56:55.125403", "user_id": 71}, {"address": "123 Test Street", "created_at": "2025-05-29T05:57:34.585088", "date_of_birth": "2005-01-15", "first_name": "Test", "id": 25, "last_name": "Student", "phone": "**********", "updated_at": "2025-05-29T05:57:34.585093", "user_id": 74}, {"address": "123 Test Street", "created_at": "2025-05-29T06:00:08.169156", "date_of_birth": "2005-01-15", "first_name": "Integrity", "id": 26, "last_name": "Student", "phone": "**********", "updated_at": "2025-05-29T06:00:08.169160", "user_id": 86}, {"address": "123 Test Street", "created_at": "2025-05-29T06:19:45.055969", "date_of_birth": "2005-01-15", "first_name": "Integrity", "id": 27, "last_name": "Student", "phone": "**********", "updated_at": "2025-05-29T06:19:45.055976", "user_id": 89}]}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:35.389911", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 201, "response": {"message": "User registered successfully", "user": {"course": null, "created_at": "2025-05-29T06:20:35.384037", "email": "<EMAIL>", "id": 93, "is_admin": false, "main_code": null, "role": "Student", "updated_at": "2025-05-29T06:20:35.384041", "username": "student_user_20250529_115033"}}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:37.420873", "endpoint": "http://localhost:5002/api/students/students", "method": "POST", "status_code": 201, "response": {"message": "Student registered successfully", "student": {"address": "123 Test Street", "created_at": "2025-05-29T06:20:37.417786", "date_of_birth": "2005-01-15", "first_name": "Test", "id": 28, "last_name": "Student", "phone": "**********", "updated_at": "2025-05-29T06:20:37.417791", "user_id": 93}}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:39.446241", "endpoint": "http://localhost:5002/api/students/students/28", "method": "GET", "status_code": 200, "response": {"student": {"address": "123 Test Street", "created_at": "2025-05-29T06:20:37.417786", "date_of_birth": "2005-01-15", "first_name": "Test", "id": 28, "last_name": "Student", "phone": "**********", "updated_at": "2025-05-29T06:20:37.417791", "user_id": 93}}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:41.465875", "endpoint": "http://localhost:5004/api/parents/health", "method": "GET", "status_code": 200, "response": {"service": "parent", "status": "healthy"}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:43.494504", "endpoint": "http://localhost:5004/api/parents/parents", "method": "GET", "status_code": 200, "response": {"parents": [{"address": "456 Parent St", "created_at": "2025-05-14T13:24:30.682268", "first_name": "<PERSON><PERSON>", "id": 3, "last_name": "Parent", "occupation": "Engineer", "phone": "**********", "updated_at": "2025-05-14T13:24:30.682276", "user_id": 34}, {"address": "new india colonoy", "created_at": "2025-05-15T05:06:59.055797", "first_name": "<PERSON>", "id": 4, "last_name": "s", "occupation": "farmer", "phone": "**********", "updated_at": "2025-05-15T05:06:59.055819", "user_id": 38}, {"address": "new india colonoy", "created_at": "2025-05-15T05:12:59.563069", "first_name": "a<PERSON>l", "id": 5, "last_name": "bro", "occupation": "farmer", "phone": "**********", "updated_at": "2025-05-15T05:12:59.563073", "user_id": 40}, {"address": "new india colonoy", "created_at": "2025-05-15T05:16:39.715982", "first_name": "<PERSON><PERSON>", "id": 6, "last_name": "r", "occupation": "farmer", "phone": "**********", "updated_at": "2025-05-15T05:16:39.715990", "user_id": 42}, {"address": "new india colonoy", "created_at": "2025-05-15T10:41:10.128690", "first_name": "vijay", "id": 7, "last_name": "m", "occupation": "farmer", "phone": "**********", "updated_at": "2025-05-15T10:41:10.128786", "user_id": 49}, {"address": "new india colonoy", "created_at": "2025-05-27T10:13:26.464530", "first_name": "rag<PERSON>", "id": 8, "last_name": "bro", "occupation": "farmer", "phone": "**********", "updated_at": "2025-05-27T10:13:26.464549", "user_id": 57}, {"address": "new india colonoy", "created_at": "2025-05-27T10:20:09.079861", "first_name": "mallika", "id": 9, "last_name": "podi", "occupation": "farmer", "phone": "**********", "updated_at": "2025-05-27T10:20:09.079872", "user_id": 58}, {"address": "456 Test Avenue", "created_at": "2025-05-29T05:53:39.806496", "first_name": "Test", "id": 10, "last_name": "Parent", "occupation": "Engineer", "phone": "**********", "updated_at": "2025-05-29T05:53:39.806503", "user_id": 70}, {"address": "456 Test Avenue", "created_at": "2025-05-29T05:56:59.432662", "first_name": "Test", "id": 11, "last_name": "Parent", "occupation": "Engineer", "phone": "**********", "updated_at": "2025-05-29T05:56:59.432669", "user_id": 72}, {"address": "456 Test Avenue", "created_at": "2025-05-29T05:57:44.908771", "first_name": "Test", "id": 12, "last_name": "Parent", "occupation": "Engineer", "phone": "**********", "updated_at": "2025-05-29T05:57:44.908775", "user_id": 75}, {"address": "456 Test Avenue", "created_at": "2025-05-29T06:00:12.431035", "first_name": "Parent0", "id": 13, "last_name": "Test", "occupation": "Engineer", "phone": "**********", "updated_at": "2025-05-29T06:00:12.431040", "user_id": 87}, {"address": "456 Test Avenue", "created_at": "2025-05-29T06:00:16.709197", "first_name": "Parent1", "id": 14, "last_name": "Test", "occupation": "Engineer", "phone": "**********", "updated_at": "2025-05-29T06:00:16.709202", "user_id": 88}, {"address": "456 Test Avenue", "created_at": "2025-05-29T06:19:49.309251", "first_name": "Parent0", "id": 15, "last_name": "Test", "occupation": "Engineer", "phone": "**********", "updated_at": "2025-05-29T06:19:49.309259", "user_id": 90}, {"address": "456 Test Avenue", "created_at": "2025-05-29T06:19:53.585006", "first_name": "Parent1", "id": 16, "last_name": "Test", "occupation": "Engineer", "phone": "**********", "updated_at": "2025-05-29T06:19:53.585011", "user_id": 91}]}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:45.747430", "endpoint": "http://localhost:5001/api/users/register", "method": "POST", "status_code": 201, "response": {"message": "User registered successfully", "user": {"course": null, "created_at": "2025-05-29T06:20:45.740223", "email": "<EMAIL>", "id": 94, "is_admin": false, "main_code": null, "role": "Parent", "updated_at": "2025-05-29T06:20:45.740229", "username": "parent_user_20250529_115043"}}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:47.789441", "endpoint": "http://localhost:5004/api/parents/parents", "method": "POST", "status_code": 201, "response": {"message": "Parent registered successfully", "parent": {"address": "456 Test Avenue", "created_at": "2025-05-29T06:20:47.782897", "first_name": "Test", "id": 17, "last_name": "Parent", "occupation": "Engineer", "phone": "**********", "updated_at": "2025-05-29T06:20:47.782903", "user_id": 94}}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:49.832111", "endpoint": "http://localhost:5004/api/parents/parents/17", "method": "GET", "status_code": 200, "response": {"parent": {"address": "456 Test Avenue", "created_at": "2025-05-29T06:20:47.782897", "first_name": "Test", "id": 17, "last_name": "Parent", "occupation": "Engineer", "phone": "**********", "updated_at": "2025-05-29T06:20:47.782903", "user_id": 94}}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:51.861408", "endpoint": "http://localhost:5003/api/courses/health", "method": "GET", "status_code": 200, "response": {"service": "course", "status": "healthy"}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:53.922228", "endpoint": "http://localhost:5003/api/courses/courses", "method": "GET", "status_code": 200, "response": {"courses": [{"created_at": "2025-05-14T13:26:27.232467", "description": "Advanced course for JEE preparation", "id": 6, "name": "<PERSON>e Advanced", "updated_at": "2025-05-14T13:26:27.232474"}, {"created_at": "2025-05-15T05:00:23.943386", "description": "Advanced course for NEET preparation", "id": 7, "name": "<PERSON><PERSON><PERSON>", "updated_at": "2025-05-15T05:00:23.943406"}, {"created_at": "2025-05-15T05:12:01.851540", "description": "Advanced course for Science preparation", "id": 8, "name": "science", "updated_at": "2025-05-15T05:12:01.851545"}, {"created_at": "2025-05-27T10:11:32.478906", "description": "Advanced course for JEE preparation", "id": 9, "name": "maths", "updated_at": "2025-05-27T10:11:32.478925"}, {"created_at": "2025-05-28T04:52:41.367881", "description": "hi", "id": 10, "name": "English", "updated_at": "2025-05-28T04:52:41.367888"}, {"created_at": "2025-05-29T05:51:03.112513", "description": "Test course for API testing", "id": 11, "name": "Test Course 20250529_112101", "updated_at": "2025-05-29T05:51:03.112520"}, {"created_at": "2025-05-29T05:53:47.907009", "description": "Test course for API testing", "id": 12, "name": "Test Course 20250529_112345", "updated_at": "2025-05-29T05:53:47.907013"}, {"created_at": "2025-05-29T05:57:53.021567", "description": "Test course for API testing", "id": 13, "name": "Test Course 20250529_112751", "updated_at": "2025-05-29T05:57:53.021574"}]}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:55.965291", "endpoint": "http://localhost:5003/api/courses/courses", "method": "POST", "status_code": 201, "response": {"course": {"created_at": "2025-05-29T06:20:55.959788", "description": "Test course for API testing", "id": 14, "name": "Test Course 20250529_115053", "updated_at": "2025-05-29T06:20:55.959796"}, "message": "Course created successfully"}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:50:58.005752", "endpoint": "http://localhost:5003/api/courses/courses/14", "method": "GET", "status_code": 200, "response": {"course": {"created_at": "2025-05-29T06:20:55.959788", "description": "Test course for API testing", "id": 14, "name": "Test Course 20250529_115053", "updated_at": "2025-05-29T06:20:55.959796"}}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:51:02.075140", "endpoint": "http://localhost:5002/api/students/map-parent", "method": "POST", "status_code": 201, "response": {"mapping": {"created_at": "2025-05-29T06:21:02.068562", "id": 20, "parent_id": 17, "relationship": "Father", "student": {"address": "123 Test Street", "created_at": "2025-05-29T06:20:37.417786", "date_of_birth": "2005-01-15", "first_name": "Test", "id": 28, "last_name": "Student", "phone": "**********", "updated_at": "2025-05-29T06:20:37.417791", "user_id": 93}, "student_id": 28}, "message": "Parent mapped to student successfully"}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:51:04.109167", "endpoint": "http://localhost:5002/api/students/parent-students/17", "method": "GET", "status_code": 200, "response": {"students": [{"created_at": "2025-05-29T06:21:02.068562", "id": 20, "parent_id": 17, "relationship": "Father", "student": {"address": "123 Test Street", "created_at": "2025-05-29T06:20:37.417786", "date_of_birth": "2005-01-15", "first_name": "Test", "id": 28, "last_name": "Student", "phone": "**********", "updated_at": "2025-05-29T06:20:37.417791", "user_id": 93}, "student_id": 28}]}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:51:06.145262", "endpoint": "http://localhost:5003/api/courses/map-student", "method": "POST", "status_code": 201, "response": {"mapping": {"course": {"created_at": "2025-05-29T06:20:55.959788", "description": "Test course for API testing", "id": 14, "name": "Test Course 20250529_115053", "updated_at": "2025-05-29T06:20:55.959796"}, "course_id": 14, "created_at": "2025-05-29T06:21:06.141539", "id": 14, "student_id": 28}, "message": "Student mapped to course successfully"}, "error": null, "success": true}, {"timestamp": "2025-05-29T11:51:08.180756", "endpoint": "http://localhost:5003/api/courses/student-courses/28", "method": "GET", "status_code": 200, "response": {"courses": [{"course": {"created_at": "2025-05-29T06:20:55.959788", "description": "Test course for API testing", "id": 14, "name": "Test Course 20250529_115053", "updated_at": "2025-05-29T06:20:55.959796"}, "course_id": 14, "created_at": "2025-05-29T06:21:06.141539", "id": 14, "student_id": 28}]}, "error": null, "success": true}]