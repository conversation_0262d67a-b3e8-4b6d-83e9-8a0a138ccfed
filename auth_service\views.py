"""
Views for the Auth Service.

This module defines the API endpoints for the Auth Service.

English: This file defines the API endpoints for the Auth Service
Tanglish: Indha file Auth Service-kku API endpoints-a define pannum
"""

from flask import Blueprint, jsonify
from auth_service.controllers import login, verify_token

# Create a Blueprint for auth routes
auth_bp = Blueprint('auth', __name__)

# Add route handlers for OPTIONS requests
@auth_bp.route('/<path:path>', methods=['OPTIONS'])
def handle_options_path(path):
    """
    Handle OPTIONS requests for CORS preflight with path.

    Returns:
        Empty response with appropriate CORS headers

    English: This endpoint handles OPTIONS requests for CORS
    Tanglish: Indha endpoint CORS-kku OPTIONS requests-a handle pannum
    """
    return "", 200

@auth_bp.route('/', methods=['OPTIONS'])
def handle_options_root():
    """
    Handle OPTIONS requests for CORS preflight at root.

    Returns:
        Empty response with appropriate CORS headers

    English: This endpoint handles OPTIONS requests for CORS at root
    Tanglish: Indha endpoint CORS-kku OPTIONS requests-a root-la handle pannum
    """
    return "", 200

@auth_bp.route('/login', methods=['POST'])
def login_route():
    """
    Login endpoint.

    Returns:
        Response from login controller

    English: This endpoint handles user login
    Tanglish: Indha endpoint user login-a handle pannum
    """
    print("Login route accessed")
    return login()

@auth_bp.route('/verify', methods=['GET'])
def verify_route():
    """
    Token verification endpoint.

    Returns:
        Response from verify_token controller

    English: This endpoint checks if a token is valid
    Tanglish: Indha endpoint token valid-a irukka nu check pannum
    """
    return verify_token()

@auth_bp.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint.

    Returns:
        JSON response indicating the service is running

    English: This endpoint checks if the service is running
    Tanglish: Indha endpoint service odi kondu irukka nu check pannum
    """
    return jsonify({"status": "healthy", "service": "auth"})
