/**
 * Panel Field Usage Guide
 * 
 * This component demonstrates how to use the custom panel field components
 * (Button.jsx, Input.jsx, DatepickerComponent.jsx) throughout the application
 * with proper role-specific styling.
 */

import { useState } from 'react';
import Button from '../field/Button';
import Input from '../field/Input';
import DatepickerComponent from '../field/DatepickerComponent';

const PanelFieldUsageGuide = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    email: '',
    firstName: '',
    lastName: '',
    dateOfBirth: '',
    address: '',
    phone: ''
  });

  const [loading, setLoading] = useState(false);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      alert('Form submitted successfully!');
    }, 2000);
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Panel Field Components Usage Guide
        </h1>
        <p className="text-lg text-gray-600">
          Demonstrating consistent usage of Button.jsx, Input.jsx, and DatepickerComponent.jsx
          with role-specific styling across all user panels.
        </p>
      </div>

      {/* Super Admin Panel Example */}
      <div className="bg-white rounded-lg shadow-lg p-6 border-t-4 border-purple-500">
        <h2 className="text-2xl font-bold mb-6 text-purple-700">Super Admin Panel Example</h2>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Input
              id="super-admin-username"
              name="username"
              type="text"
              label="Username"
              value={formData.username}
              onChange={handleInputChange}
              placeholder="Enter username"
              required
              userRole="super_admin"
            />
            
            <Input
              id="super-admin-password"
              name="password"
              type="password"
              label="Password"
              value={formData.password}
              onChange={handleInputChange}
              placeholder="Enter password"
              required
              userRole="super_admin"
            />
            
            <Input
              id="super-admin-email"
              name="email"
              type="email"
              label="Email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder="Enter email"
              required
              userRole="super_admin"
            />
            
            <DatepickerComponent
              id="super-admin-dob"
              name="dateOfBirth"
              label="Date of Birth"
              value={formData.dateOfBirth}
              onChange={handleInputChange}
              userRole="super_admin"
            />
          </div>
          
          <div className="flex gap-4">
            <Button
              type="submit"
              variant="primary"
              size="md"
              userRole="super_admin"
              loading={loading}
              disabled={loading}
            >
              Save Super Admin Data
            </Button>
            
            <Button
              type="button"
              variant="secondary"
              size="md"
              userRole="super_admin"
            >
              Cancel
            </Button>
            
            <Button
              type="button"
              variant="danger"
              size="md"
              userRole="super_admin"
            >
              Delete
            </Button>
          </div>
        </form>
      </div>

      {/* Admin Panel Example */}
      <div className="bg-white rounded-lg shadow-lg p-6 border-t-4 border-green-500">
        <h2 className="text-2xl font-bold mb-6 text-green-700">Admin Panel Example</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            id="admin-first-name"
            name="firstName"
            type="text"
            label="First Name"
            value={formData.firstName}
            onChange={handleInputChange}
            placeholder="Enter first name"
            userRole="admin"
          />
          
          <Input
            id="admin-last-name"
            name="lastName"
            type="text"
            label="Last Name"
            value={formData.lastName}
            onChange={handleInputChange}
            placeholder="Enter last name"
            userRole="admin"
          />
          
          <Input
            id="admin-address"
            name="address"
            type="text"
            label="Address"
            value={formData.address}
            onChange={handleInputChange}
            placeholder="Enter address"
            userRole="admin"
          />
          
          <Input
            id="admin-phone"
            name="phone"
            type="tel"
            label="Phone"
            value={formData.phone}
            onChange={handleInputChange}
            placeholder="Enter phone number"
            userRole="admin"
          />
        </div>
        
        <div className="mt-6 flex gap-4">
          <Button
            type="button"
            variant="primary"
            size="lg"
            userRole="admin"
          >
            Register Teacher
          </Button>
          
          <Button
            type="button"
            variant="success"
            size="md"
            userRole="admin"
          >
            Approve
          </Button>
          
          <Button
            type="button"
            variant="warning"
            size="sm"
            userRole="admin"
          >
            Warning
          </Button>
        </div>
      </div>

      {/* Teacher Panel Example */}
      <div className="bg-white rounded-lg shadow-lg p-6 border-t-4 border-indigo-500">
        <h2 className="text-2xl font-bold mb-6 text-indigo-700">Teacher Panel Example</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Input
            id="teacher-subject"
            name="subject"
            type="text"
            label="Subject"
            placeholder="Enter subject"
            userRole="teacher"
          />
          
          <DatepickerComponent
            id="teacher-class-date"
            name="classDate"
            label="Class Date"
            userRole="teacher"
          />
          
          <Input
            id="teacher-duration"
            name="duration"
            type="number"
            label="Duration (hours)"
            placeholder="Enter duration"
            userRole="teacher"
          />
        </div>
        
        <div className="mt-6 flex gap-4">
          <Button
            type="button"
            variant="primary"
            userRole="teacher"
            fullWidth={false}
          >
            Schedule Class
          </Button>
          
          <Button
            type="button"
            variant="info"
            userRole="teacher"
          >
            View Students
          </Button>
        </div>
      </div>

      {/* Student Panel Example */}
      <div className="bg-white rounded-lg shadow-lg p-6 border-t-4 border-blue-500">
        <h2 className="text-2xl font-bold mb-6 text-blue-700">Student Panel Example</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            id="student-assignment"
            name="assignment"
            type="text"
            label="Assignment Title"
            placeholder="Enter assignment title"
            userRole="student"
          />
          
          <DatepickerComponent
            id="student-due-date"
            name="dueDate"
            label="Due Date"
            userRole="student"
          />
        </div>
        
        <div className="mt-6 flex gap-4">
          <Button
            type="button"
            variant="primary"
            userRole="student"
          >
            Submit Assignment
          </Button>
          
          <Button
            type="button"
            variant="secondary"
            userRole="student"
          >
            Save Draft
          </Button>
        </div>
      </div>

      {/* Parent Panel Example */}
      <div className="bg-white rounded-lg shadow-lg p-6 border-t-4 border-orange-500">
        <h2 className="text-2xl font-bold mb-6 text-orange-700">Parent Panel Example</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Input
            id="parent-child-name"
            name="childName"
            type="text"
            label="Child's Name"
            placeholder="Enter child's name"
            userRole="parent"
          />
          
          <DatepickerComponent
            id="parent-meeting-date"
            name="meetingDate"
            label="Meeting Date"
            userRole="parent"
          />
        </div>
        
        <div className="mt-6 flex gap-4">
          <Button
            type="button"
            variant="primary"
            userRole="parent"
          >
            Schedule Meeting
          </Button>
          
          <Button
            type="button"
            variant="info"
            userRole="parent"
          >
            View Progress
          </Button>
        </div>
      </div>

      {/* Usage Instructions */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h2 className="text-2xl font-bold mb-4 text-gray-800">Usage Instructions</h2>
        <div className="space-y-4 text-gray-700">
          <div>
            <h3 className="font-semibold text-lg mb-2">1. Always use userRole prop:</h3>
            <p>Pass the appropriate userRole ("super_admin", "admin", "teacher", "student", "parent") to get role-specific styling.</p>
          </div>
          
          <div>
            <h3 className="font-semibold text-lg mb-2">2. Button variants available:</h3>
            <p>primary, secondary, danger, success, warning, info - each adapts to the user role colors.</p>
          </div>
          
          <div>
            <h3 className="font-semibold text-lg mb-2">3. Input types supported:</h3>
            <p>text, password, email, tel, number, etc. - all with built-in validation patterns.</p>
          </div>
          
          <div>
            <h3 className="font-semibold text-lg mb-2">4. DatepickerComponent features:</h3>
            <p>Built-in calendar icon, min/max date validation, role-specific styling.</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PanelFieldUsageGuide;
