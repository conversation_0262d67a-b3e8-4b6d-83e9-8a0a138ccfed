"""
<PERSON><PERSON><PERSON> to test the user service endpoints.

This script tests the user service endpoints by getting all users and registering a new user.
"""

import requests
import jwt
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get JWT secret from environment variables
JWT_SECRET = os.environ.get('JWT_SECRET', 'simple-jwt-secret-for-school-management-system')

def generate_token(user_id, username, role, is_admin=False):
    """
    Generate a JWT token.
    
    Args:
        user_id: User ID
        username: Username
        role: User role
        is_admin: Whether the user is an admin
        
    Returns:
        JWT token string
    """
    # Set token expiration time (e.g., 24 hours)
    expiration = datetime.utcnow() + timedelta(hours=24)
    
    # Create token payload
    payload = {
        'sub': str(user_id),  # Convert user ID to string
        'username': username,
        'role': role,
        'is_admin': is_admin,
        'exp': expiration
    }
    
    # Generate token
    token = jwt.encode(payload, JWT_SECRET, algorithm='HS256')
    
    return token

def get_users(token):
    """
    Get all users.
    
    Args:
        token: JWT token string
        
    Returns:
        Response from the endpoint
    """
    url = 'http://localhost:5001/api/users/users'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    print(f"Sending GET request to {url}")
    print(f"Headers: {headers}")
    
    response = requests.get(url, headers=headers)
    
    print(f"Response status code: {response.status_code}")
    print(f"Response content: {response.text}")
    
    return response

def register_user(token, username, email, password, role):
    """
    Register a new user.
    
    Args:
        token: JWT token string
        username: Username
        email: Email
        password: Password
        role: User role
        
    Returns:
        Response from the endpoint
    """
    url = 'http://localhost:5001/api/users/register'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    data = {
        'username': username,
        'email': email,
        'password': password,
        'role': role
    }
    
    print(f"Sending POST request to {url}")
    print(f"Headers: {headers}")
    print(f"Data: {data}")
    
    response = requests.post(url, json=data, headers=headers)
    
    print(f"Response status code: {response.status_code}")
    print(f"Response content: {response.text}")
    
    return response

if __name__ == '__main__':
    # Generate a token for a Super Admin
    token = generate_token(9, 'superadmin', 'Super Admin', True)
    print(f"Generated token: {token}")
    
    # Test getting all users
    print("\n=== Testing GET /api/users/users ===")
    get_users(token)
    
    # Test registering a new user
    print("\n=== Testing POST /api/users/register ===")
    register_user(
        token,
        f"testuser_{datetime.now().strftime('%Y%m%d%H%M%S')}",
        f"testuser_{datetime.now().strftime('%Y%m%d%H%M%S')}@example.com",
        "password123",
        "Student"
    )
