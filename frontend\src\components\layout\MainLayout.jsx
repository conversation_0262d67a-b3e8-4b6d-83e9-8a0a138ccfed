/**
 * Main Layout Component
 *
 * This component provides the main layout structure for authenticated pages.
 * It includes the navbar, sidebar, and main content area.
 */

import { useState } from 'react';
import Navbar from './Navbar';
import Sidebar from './Sidebar';

const MainLayout = ({ children, title }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="flex flex-col min-h-screen bg-gray-100">
      {/* Navbar */}
      <Navbar />

      <div className="flex flex-1">
        {/* Sidebar */}
        <Sidebar
          isOpen={isSidebarOpen}
          onToggle={toggleSidebar}
          variant="default"
        />

        {/* Main content */}
        <main className="flex-1 p-6">
          <div className="container mx-auto">
            {/* Toggle sidebar button (mobile only) */}
            <button
              onClick={toggleSidebar}
              className="md:hidden mb-4 p-2 rounded-md bg-gray-200 hover:bg-gray-300 focus:outline-none"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>

            {/* Page title */}
            {title && (
              <h1 className="text-2xl font-bold mb-6">{title}</h1>
            )}

            {/* Page content */}
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
