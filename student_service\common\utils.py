"""
Utility functions shared across microservices.

This module provides common utility functions that are used by multiple microservices.

English: This file contains helper functions used by all services
Tanglish: Indha file-la ella services-um use panna helper functions irukku
"""

import json
import logging
import requests
from functools import wraps
from flask import jsonify, request

# Configure logging
logging.basicConfig(
    filename='app.log',
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def handle_error(error_message, status_code=400):
    """
    Create a standardized error response.
    
    Args:
        error_message: The error message to return
        status_code: HTTP status code (default: 400)
        
    Returns:
        JSON response with error message and status code
        
    English: This function creates a standard error response with message and status code
    Tanglish: Indha function standard error response-a create pannum, message-um status code-um set pannum
    """
    logger.error(f"Error: {error_message}, Status: {status_code}")
    response = jsonify({"error": error_message})
    response.status_code = status_code
    return response

def validate_request_data(required_fields):
    """
    Decorator to validate that required fields are present in the request data.
    
    Args:
        required_fields: List of field names that must be present
        
    Returns:
        Decorator function
        
    English: This decorator checks if all required fields are in the request
    Tanglish: Indha decorator request-la ella required fields-um irukka nu check pannum
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            data = request.get_json()
            if not data:
                return handle_error("No data provided", 400)
                
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                return handle_error(f"Missing required fields: {', '.join(missing_fields)}", 400)
                
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def call_service(service_url, method='GET', data=None, headers=None):
    """
    Make an HTTP request to another microservice.
    
    Args:
        service_url: URL of the service to call
        method: HTTP method (GET, POST, etc.)
        data: Data to send in the request
        headers: Headers to include in the request
        
    Returns:
        Response from the service
        
    English: This function calls another microservice using HTTP
    Tanglish: Indha function vera oru microservice-a HTTP moolama call pannum
    """
    default_headers = {'Content-Type': 'application/json'}
    if headers:
        default_headers.update(headers)
        
    try:
        if method.upper() == 'GET':
            response = requests.get(service_url, headers=default_headers)
        elif method.upper() == 'POST':
            response = requests.post(service_url, json=data, headers=default_headers)
        elif method.upper() == 'PUT':
            response = requests.put(service_url, json=data, headers=default_headers)
        elif method.upper() == 'DELETE':
            response = requests.delete(service_url, headers=default_headers)
        else:
            logger.error(f"Unsupported HTTP method: {method}")
            return None
            
        return response
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Error calling service {service_url}: {str(e)}")
        return None
