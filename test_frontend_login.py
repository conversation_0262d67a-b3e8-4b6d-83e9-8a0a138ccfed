"""
Script to test frontend login flow by simulating the authService.login method.
"""

import requests
import json

def test_frontend_login_flow():
    """Test the frontend login flow and check localStorage simulation."""
    print("Testing frontend login flow...")
    
    # Simulate the frontend authService.login method
    login_url = "http://localhost:5000/api/auth/login"
    login_data = {
        "username": "teacher1",
        "password": "password123"
    }
    
    try:
        # Step 1: Make login request (same as frontend)
        response = requests.post(login_url, json=login_data, headers={
            'Content-Type': 'application/json'
        })
        
        print(f"Login response status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            token = response_data.get('token')
            user_data = response_data.get('user', {})
            
            print(f"Token received: {token[:50]}...")
            print(f"User data: {json.dumps(user_data, indent=2)}")
            
            # Step 2: Simulate frontend localStorage logic
            main_code = None
            
            # Check if user is Teacher or Admin (same logic as frontend)
            if user_data and user_data.get('role') in ['Teacher', 'Admin']:
                # Handle maincode vs main_code compatibility
                if user_data.get('maincode') and not user_data.get('main_code'):
                    user_data['main_code'] = user_data['maincode']
                    del user_data['maincode']
                
                # Get main_code for localStorage
                main_code = user_data.get('main_code')
                
                if main_code:
                    print(f"\n✅ main_code '{main_code}' would be stored in localStorage!")
                    print(f"localStorage.setItem('main_code', '{main_code}')")
                else:
                    print(f"\n❌ No main_code found to store in localStorage!")
            
            # Step 3: Simulate authService.getMainCode() method
            print(f"\n--- Simulating authService.getMainCode() ---")
            stored_main_code = main_code  # This would come from localStorage
            
            if stored_main_code:
                print(f"✅ getMainCode() would return: '{stored_main_code}'")
            else:
                # Fallback to user data
                fallback_main_code = user_data.get('main_code')
                if fallback_main_code:
                    print(f"✅ getMainCode() would return from user data: '{fallback_main_code}'")
                else:
                    print(f"❌ getMainCode() would return null - this would cause the error!")
            
            return True
            
        else:
            print(f"❌ Login failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"Error during login test: {str(e)}")
        return False

if __name__ == '__main__':
    test_frontend_login_flow()
