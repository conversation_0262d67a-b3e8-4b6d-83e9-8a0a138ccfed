#!/usr/bin/env python3
"""
Security and Data Integrity Testing Script
Tests for SQL injection, XSS, CSRF, authentication flaws, and data integrity issues
"""

import requests
import json
from datetime import datetime

# Base URLs
BASE_URLS = {
    'auth': 'http://localhost:5000/api/auth',
    'users': 'http://localhost:5001/api/users', 
    'students': 'http://localhost:5002/api/students',
    'courses': 'http://localhost:5003/api/courses',
    'parents': 'http://localhost:5004/api/parents'
}

# Global variables
access_token = None
test_results = []

def log_security_test(test_name, endpoint, method, status_code, response_data, vulnerability_found=False, details=""):
    """Log security test results"""
    result = {
        'timestamp': datetime.now().isoformat(),
        'test_name': test_name,
        'endpoint': endpoint,
        'method': method,
        'status_code': status_code,
        'response': response_data,
        'vulnerability_found': vulnerability_found,
        'details': details,
        'security_status': "VULNERABLE" if vulnerability_found else "SECURE"
    }
    test_results.append(result)
    
    status = "🚨 VULNERABLE" if vulnerability_found else "🔒 SECURE"
    print(f"{status} {test_name} - {method} {endpoint} - Status: {status_code}")
    if details:
        print(f"   Details: {details}")
    return result

def get_auth_token():
    """Get authentication token"""
    login_data = {
        "username": "superadmin",
        "password": "D@qwertyuiop"
    }
    
    response = requests.post(f"{BASE_URLS['auth']}/login", json=login_data)
    if response.status_code == 200:
        global access_token
        access_token = response.json()['token']
        return True
    return False

def test_sql_injection():
    """Test for SQL injection vulnerabilities"""
    print("\n🔍 Testing SQL Injection Vulnerabilities...")
    
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    
    # SQL injection payloads
    sql_payloads = [
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "' UNION SELECT * FROM users --",
        "admin'--",
        "' OR 1=1 --"
    ]
    
    # Test login endpoint
    for payload in sql_payloads:
        login_data = {
            "username": payload,
            "password": "test"
        }
        
        response = requests.post(f"{BASE_URLS['auth']}/login", json=login_data)
        
        # Check if SQL injection was successful (should not be)
        vulnerability_found = response.status_code == 200 or "error" not in response.text.lower()
        
        log_security_test(
            "SQL Injection - Login",
            f"{BASE_URLS['auth']}/login",
            "POST",
            response.status_code,
            response.text[:100],
            vulnerability_found,
            f"Payload: {payload}"
        )
    
    # Test user registration with SQL injection
    for payload in sql_payloads[:2]:  # Test fewer payloads for registration
        user_data = {
            "username": payload,
            "email": f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
            "password": "TestPass123!",
            "role": "Student"
        }
        
        response = requests.post(f"{BASE_URLS['users']}/register", json=user_data, headers=headers)
        
        # Check if malicious data was accepted
        vulnerability_found = response.status_code == 201
        
        log_security_test(
            "SQL Injection - User Registration",
            f"{BASE_URLS['users']}/register",
            "POST",
            response.status_code,
            response.text[:100],
            vulnerability_found,
            f"Payload: {payload}"
        )

def test_xss_vulnerabilities():
    """Test for XSS vulnerabilities"""
    print("\n🔍 Testing XSS Vulnerabilities...")
    
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    
    # XSS payloads
    xss_payloads = [
        "<script>alert('XSS')</script>",
        "javascript:alert('XSS')",
        "<img src=x onerror=alert('XSS')>",
        "';alert('XSS');//"
    ]
    
    # Test user registration with XSS payloads
    for payload in xss_payloads:
        user_data = {
            "username": f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "email": f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
            "password": "TestPass123!",
            "role": "Student"
        }
        
        # Try XSS in different fields
        for field in ['username', 'email']:
            test_data = user_data.copy()
            if field == 'email':
                test_data[field] = f"{payload}@example.com"
            else:
                test_data[field] = payload
            
            response = requests.post(f"{BASE_URLS['users']}/register", json=test_data, headers=headers)
            
            # Check if XSS payload was accepted and stored
            vulnerability_found = response.status_code == 201
            
            log_security_test(
                f"XSS - User Registration ({field})",
                f"{BASE_URLS['users']}/register",
                "POST",
                response.status_code,
                response.text[:100],
                vulnerability_found,
                f"Payload in {field}: {payload}"
            )

def test_authentication_bypass():
    """Test for authentication bypass vulnerabilities"""
    print("\n🔍 Testing Authentication Bypass...")
    
    # Test accessing protected endpoints without token
    protected_endpoints = [
        (f"{BASE_URLS['users']}/users", "GET"),
        (f"{BASE_URLS['students']}/students", "GET"),
        (f"{BASE_URLS['parents']}/parents", "GET"),
        (f"{BASE_URLS['courses']}/courses", "POST")
    ]
    
    for endpoint, method in protected_endpoints:
        if method == "GET":
            response = requests.get(endpoint)
        else:
            response = requests.post(endpoint, json={"test": "data"})
        
        # Should return 401 or 403 for unauthorized access
        vulnerability_found = response.status_code not in [401, 403]
        
        log_security_test(
            "Authentication Bypass",
            endpoint,
            method,
            response.status_code,
            response.text[:100],
            vulnerability_found,
            "Attempted access without authentication token"
        )
    
    # Test with invalid token
    invalid_headers = {'Authorization': 'Bearer invalid_token_here', 'Content-Type': 'application/json'}
    
    response = requests.get(f"{BASE_URLS['users']}/users", headers=invalid_headers)
    vulnerability_found = response.status_code not in [401, 403]
    
    log_security_test(
        "Invalid Token Test",
        f"{BASE_URLS['users']}/users",
        "GET",
        response.status_code,
        response.text[:100],
        vulnerability_found,
        "Attempted access with invalid token"
    )

def test_data_integrity():
    """Test data integrity constraints"""
    print("\n🔍 Testing Data Integrity...")
    
    headers = {'Authorization': f'Bearer {access_token}', 'Content-Type': 'application/json'}
    
    # Test one student to multiple parents constraint
    # First create a student
    user_data = {
        "username": f"integrity_student_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "email": f"integrity_student_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
        "password": "TestPass123!",
        "role": "Student"
    }
    
    user_response = requests.post(f"{BASE_URLS['users']}/register", json=user_data, headers=headers)
    if user_response.status_code == 201:
        user_id = user_response.json()['user']['id']
        
        student_data = {
            "user_id": user_id,
            "first_name": "Integrity",
            "last_name": "Student",
            "date_of_birth": "2005-01-15",
            "address": "123 Test Street",
            "phone": "1234567890"
        }
        
        student_response = requests.post(f"{BASE_URLS['students']}/students", json=student_data, headers=headers)
        if student_response.status_code == 201:
            student_id = student_response.json()['student']['id']
            
            # Create two parents
            parent_ids = []
            for i in range(2):
                parent_user_data = {
                    "username": f"integrity_parent_{i}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    "email": f"integrity_parent_{i}_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
                    "password": "TestPass123!",
                    "role": "Parent"
                }
                
                parent_user_response = requests.post(f"{BASE_URLS['users']}/register", json=parent_user_data, headers=headers)
                if parent_user_response.status_code == 201:
                    parent_user_id = parent_user_response.json()['user']['id']
                    
                    parent_data = {
                        "user_id": parent_user_id,
                        "first_name": f"Parent{i}",
                        "last_name": "Test",
                        "occupation": "Engineer",
                        "address": "456 Test Avenue",
                        "phone": "9876543210"
                    }
                    
                    parent_response = requests.post(f"{BASE_URLS['parents']}/parents", json=parent_data, headers=headers)
                    if parent_response.status_code == 201:
                        parent_ids.append(parent_response.json()['parent']['id'])
            
            # Try to map first parent to student
            if len(parent_ids) >= 1:
                mapping_data = {
                    "student_id": student_id,
                    "parent_id": parent_ids[0],
                    "relationship": "Father"
                }
                
                first_mapping = requests.post(f"{BASE_URLS['students']}/map-parent", json=mapping_data, headers=headers)
                
                log_security_test(
                    "Data Integrity - First Parent Mapping",
                    f"{BASE_URLS['students']}/map-parent",
                    "POST",
                    first_mapping.status_code,
                    first_mapping.text[:100],
                    first_mapping.status_code != 201,
                    "First parent mapping should succeed"
                )
                
                # Try to map second parent to same student (should fail)
                if len(parent_ids) >= 2:
                    mapping_data['parent_id'] = parent_ids[1]
                    mapping_data['relationship'] = "Mother"
                    
                    second_mapping = requests.post(f"{BASE_URLS['students']}/map-parent", json=mapping_data, headers=headers)
                    
                    # This should fail due to one-parent-per-student constraint
                    vulnerability_found = second_mapping.status_code == 201
                    
                    log_security_test(
                        "Data Integrity - Multiple Parents Constraint",
                        f"{BASE_URLS['students']}/map-parent",
                        "POST",
                        second_mapping.status_code,
                        second_mapping.text[:100],
                        vulnerability_found,
                        "Second parent mapping should be rejected (one parent per student rule)"
                    )

def test_cors_configuration():
    """Test CORS configuration"""
    print("\n🔍 Testing CORS Configuration...")
    
    # Test CORS headers
    headers = {
        'Origin': 'http://malicious-site.com',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type'
    }
    
    response = requests.options(f"{BASE_URLS['auth']}/login", headers=headers)
    
    cors_headers = response.headers
    allow_origin = cors_headers.get('Access-Control-Allow-Origin', '')
    
    # Check if CORS is too permissive
    vulnerability_found = allow_origin == '*' or 'malicious-site.com' in allow_origin
    
    log_security_test(
        "CORS Configuration",
        f"{BASE_URLS['auth']}/login",
        "OPTIONS",
        response.status_code,
        str(dict(cors_headers)),
        vulnerability_found,
        f"Access-Control-Allow-Origin: {allow_origin}"
    )

def generate_security_report():
    """Generate comprehensive security test report"""
    print("\n" + "="*80)
    print("🔒 COMPREHENSIVE SECURITY TEST REPORT")
    print("="*80)
    
    total_tests = len(test_results)
    secure_tests = len([r for r in test_results if not r['vulnerability_found']])
    vulnerable_tests = total_tests - secure_tests
    
    print(f"Total Security Tests: {total_tests}")
    print(f"Secure: {secure_tests} 🔒")
    print(f"Vulnerable: {vulnerable_tests} 🚨")
    print(f"Security Score: {(secure_tests/total_tests)*100:.1f}%")
    
    if vulnerable_tests > 0:
        print("\n🚨 VULNERABILITIES FOUND:")
        print("-" * 50)
        for result in test_results:
            if result['vulnerability_found']:
                print(f"❌ {result['test_name']}")
                print(f"   Endpoint: {result['endpoint']}")
                print(f"   Details: {result['details']}")
                print()
    else:
        print("\n✅ NO VULNERABILITIES FOUND!")
    
    print("\n📋 DETAILED SECURITY RESULTS:")
    print("-" * 80)
    
    for result in test_results:
        status = "🚨" if result['vulnerability_found'] else "🔒"
        print(f"{status} {result['test_name']}")
        print(f"   Endpoint: {result['method']} {result['endpoint']}")
        print(f"   Status: {result['status_code']} - {result['security_status']}")
        if result['details']:
            print(f"   Details: {result['details']}")
        print()
    
    # Save detailed report to file
    with open('security_test_report.json', 'w') as f:
        json.dump(test_results, f, indent=2)
    
    print(f"📄 Detailed security report saved to: security_test_report.json")

def main():
    """Main security test execution"""
    print("🔒 Starting Comprehensive Security Testing...")
    print("="*80)
    
    # Step 1: Authentication
    if not get_auth_token():
        print("❌ Authentication failed. Cannot proceed with security tests.")
        return
    
    print("✅ Authentication successful")
    
    # Step 2: Run security tests
    test_sql_injection()
    test_xss_vulnerabilities()
    test_authentication_bypass()
    test_data_integrity()
    test_cors_configuration()
    
    # Step 3: Generate report
    generate_security_report()

if __name__ == "__main__":
    main()
