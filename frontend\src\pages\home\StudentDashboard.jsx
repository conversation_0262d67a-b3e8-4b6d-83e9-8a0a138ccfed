/**
 * Student Dashboard Page
 *
 * This page shows the dashboard for Student users.
 *
 * English: This page shows the Student dashboard with their courses
 * Tanglish: Indha page Student-kku dashboard-a display pannum, avanga courses-oda
 */

import { useState, useEffect } from 'react';
import DashboardLayout from '../dashboard/DashboardLayout';
import { useAuth } from '../../contexts/AuthContext';
import { studentService } from '../../services/studentService';
import { courseService } from '../../services/courseService';

const StudentDashboard = () => {
  // Get current user from auth context
  const { currentUser } = useAuth();

  // State for student
  const [student, setStudent] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // State for courses
  const [courses, setCourses] = useState([]);
  const [loadingCourses, setLoadingCourses] = useState(false);

  /**
   * Load student data from API
   *
   * English: This function loads the student data from the API
   * Tanglish: Indha function API-la irundhu student data-va load pannum
   */
  const loadStudent = async () => {
    try {
      setLoading(true);
      setError('');

      // Try to get the student profile using the new method
      try {
        const data = await studentService.getStudentProfile();
        if (data && data.student) {
          setStudent(data.student);

          // Then get the student's courses
          await loadStudentCourses(data.student.id);
          return;
        }
      } catch (profileError) {
        console.warn("Error getting student profile, falling back to getStudents", profileError);
      }

      // Fallback: get the student by user ID from all students
      const students = await studentService.getStudents();
      const currentStudent = (students.students || []).find(s => s.user_id === parseInt(currentUser.id));

      if (currentStudent) {
        setStudent(currentStudent);

        // Then get the student's courses
        await loadStudentCourses(currentStudent.id);
      } else {
        setError('Student profile not found');
      }
    } catch (error) {
      setError(error.error || 'Failed to load student data');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Load student courses from API
   *
   * @param {number} studentId - ID of the student
   *
   * English: This function loads the student's courses from the API
   * Tanglish: Indha function API-la irundhu student-oda courses-a load pannum
   */
  const loadStudentCourses = async (studentId) => {
    try {
      setLoadingCourses(true);

      const data = await courseService.getStudentCourses(studentId);
      setCourses(data.courses || []);
    } catch (error) {
      console.error('Failed to load courses:', error);
    } finally {
      setLoadingCourses(false);
    }
  };

  // Load data when component mounts
  useEffect(() => {
    loadStudent();
  }, []);

  return (
    <DashboardLayout title="Student Dashboard">
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <p className="text-lg">Loading student data...</p>
        </div>
      ) : error ? (
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      ) : student ? (
        <div>
          <div className="card mb-6">
            <h2 className="text-xl font-semibold mb-4">Student Profile</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="mb-2"><strong>Name:</strong> {student.first_name} {student.last_name}</p>
                <p className="mb-2"><strong>Date of Birth:</strong> {student.date_of_birth || 'Not specified'}</p>
              </div>
              <div>
                <p className="mb-2"><strong>Address:</strong> {student.address || 'Not specified'}</p>
                <p className="mb-2"><strong>Phone:</strong> {student.phone || 'Not specified'}</p>
              </div>
            </div>
          </div>

          <div className="card">
            <h2 className="text-xl font-semibold mb-4">My Courses</h2>

            {loadingCourses ? (
              <p>Loading courses...</p>
            ) : courses.length === 0 ? (
              <p>You are not enrolled in any courses yet.</p>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {courses.map((courseMapping) => (
                  <div key={courseMapping.id} className="bg-white p-4 rounded shadow">
                    <h3 className="text-lg font-semibold">{courseMapping.course.name}</h3>
                    <p className="text-gray-600">{courseMapping.course.description || 'No description'}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="alert alert-danger" role="alert">
          No student data available.
        </div>
      )}
    </DashboardLayout>
  );
};

export default StudentDashboard;
