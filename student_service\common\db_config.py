"""
Database configuration for microservices.

This module provides functions to configure SQLAlchemy for each microservice.

English: This file helps set up the database connection for each service
Tanglish: Indha file ovvoru service-kkum database connection-a setup panna help pannum
"""

import os
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import create_engine
from sqlalchemy.orm import scoped_session, sessionmaker

# Initialize SQLAlchemy instance
db = SQLAlchemy()

def get_db_url(service_name):
    """
    Get the database URL for a specific service.

    Args:
        service_name: Name of the service (auth, user, course, student, parent)

    Returns:
        Database URL string

    English: This function gets the database connection URL for a specific service
    Tanglish: Indha function specific service-kku database connection URL-a return pannum
    """
    # Get database credentials from environment variables or use defaults
    db_user = os.environ.get('DB_USER', 'postgres')
    db_password = os.environ.get('DB_PASSWORD', 'postgres')
    db_host = os.environ.get('DB_HOST', 'localhost')
    db_port = os.environ.get('DB_PORT', '5432')

    # Each service has its own database
    db_name = f"{service_name}_db"

    # Use proper URL encoding for special characters in password
    from urllib.parse import quote_plus
    db_password = quote_plus(db_password)

    return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

def init_db(app, service_name):
    """
    Initialize the database for a Flask application.

    Args:
        app: Flask application
        service_name: Name of the service

    English: This function initializes the database for a Flask app
    Tanglish: Indha function Flask app-kku database-a initialize pannum
    """
    # Configure the SQLAlchemy part of the app
    app.config['SQLALCHEMY_DATABASE_URI'] = get_db_url(service_name)
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

    # Initialize the database with the app
    db.init_app(app)

    # Create all tables
    with app.app_context():
        db.create_all()

def create_db_engine(service_name):
    """
    Create a SQLAlchemy engine for a specific service.

    Args:
        service_name: Name of the service

    Returns:
        SQLAlchemy engine

    English: This function creates a database engine for a service
    Tanglish: Indha function service-kku database engine-a create pannum
    """
    db_url = get_db_url(service_name)
    return create_engine(db_url)

def create_db_session(engine):
    """
    Create a SQLAlchemy session factory.

    Args:
        engine: SQLAlchemy engine

    Returns:
        SQLAlchemy session factory

    English: This function creates a database session factory
    Tanglish: Indha function database session factory-a create pannum
    """
    session_factory = sessionmaker(bind=engine)
    return scoped_session(session_factory)
