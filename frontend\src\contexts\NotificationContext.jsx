/**
 * Notification Context
 *
 * This context provides notification/toast functionality to the entire application.
 * Manages success and error notifications with auto-dismiss functionality.
 */

import { createContext, useState, useContext, useCallback } from 'react';
import ToastContainer from '../components/popup/ToastContainer';

// Create the context
const NotificationContext = createContext();

// Custom hook to use the notification context
export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification must be used within a NotificationProvider');
  }
  return context;
};

// Provider component
export const NotificationProvider = ({ children, position = 'center' }) => {
  const [toasts, setToasts] = useState([]);

  // Generate unique ID for each toast
  const generateId = useCallback(() => {
    return Date.now() + Math.random().toString(36).substr(2, 9);
  }, []);

  // Add a new toast notification
  const addNotification = useCallback((message, type = 'success', duration = 5000) => {
    const id = generateId();
    const newToast = {
      id,
      message,
      type,
      duration,
      timestamp: Date.now()
    };

    setToasts(prevToasts => [...prevToasts, newToast]);
    return id;
  }, [generateId]);

  // Remove a toast notification
  const removeNotification = useCallback((id) => {
    setToasts(prevToasts => prevToasts.filter(toast => toast.id !== id));
  }, []);

  // Clear all notifications
  const clearAllNotifications = useCallback(() => {
    setToasts([]);
  }, []);

  // Convenience methods for different types of notifications
  const showSuccess = useCallback((message, duration = 5000) => {
    return addNotification(message, 'success', duration);
  }, [addNotification]);

  const showError = useCallback((message, duration = 5000) => {
    // Log error to console for debugging but don't show to user
    console.error('Error notification (hidden from user):', message);
    // Return null to indicate no notification was shown
    return null;
  }, []);

  // Auto-detect message type based on content (for backward compatibility)
  const showMessage = useCallback((message, duration = 5000) => {
    // Check if message contains success indicators
    const successKeywords = ['successfully', 'success', 'completed', 'saved', 'created', 'updated', 'deleted', 'registered', 'mapped'];
    const isSuccess = successKeywords.some(keyword =>
      message.toLowerCase().includes(keyword.toLowerCase())
    );

    if (isSuccess) {
      return addNotification(message, 'success', duration);
    } else {
      // Log error to console for debugging but don't show to user
      console.error('Error message (hidden from user):', message);
      return null;
    }
  }, [addNotification]);

  // Context value
  const value = {
    toasts,
    addNotification,
    removeNotification,
    clearAllNotifications,
    showSuccess,
    showError,
    showMessage
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
      <ToastContainer 
        toasts={toasts} 
        onRemoveToast={removeNotification}
        position={position}
      />
    </NotificationContext.Provider>
  );
};

export default NotificationContext;
