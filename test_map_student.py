"""
Script to test mapping a student to a course.

This script maps a student to a course.
"""

import requests
import jwt
import os
from datetime import datetime, timedelta
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get JWT secret from environment variables
JWT_SECRET = os.environ.get('JWT_SECRET', 'simple-jwt-secret-for-school-management-system')

def generate_token(user_id, username, role, is_admin=False):
    """
    Generate a JWT token.
    
    Args:
        user_id: User ID
        username: Username
        role: User role
        is_admin: Whether the user is an admin
        
    Returns:
        JWT token string
    """
    # Set token expiration time (e.g., 24 hours)
    expiration = datetime.utcnow() + timedelta(hours=24)
    
    # Create token payload
    payload = {
        'sub': str(user_id),  # Convert user ID to string
        'username': username,
        'role': role,
        'is_admin': is_admin,
        'exp': expiration
    }
    
    # Generate token
    token = jwt.encode(payload, JWT_SECRET, algorithm='HS256')
    
    return token

def map_student_to_course(token, student_id, course_id):
    """
    Map a student to a course.
    
    Args:
        token: JWT token string
        student_id: Student ID
        course_id: Course ID
        
    Returns:
        Response from the endpoint
    """
    url = 'http://localhost:5003/api/courses/map-student'
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    data = {
        'student_id': student_id,
        'course_id': course_id
    }
    
    print(f"Sending request to {url}")
    print(f"Headers: {headers}")
    print(f"Data: {data}")
    
    response = requests.post(url, json=data, headers=headers)
    
    print(f"Response status code: {response.status_code}")
    print(f"Response content: {response.text}")
    
    return response

if __name__ == '__main__':
    # Generate a token for a Super Admin
    token = generate_token(9, 'superadmin', 'Super Admin', True)
    print(f"Generated token: {token}")
    
    # Map student to course
    response = map_student_to_course(token, 2, 1)
