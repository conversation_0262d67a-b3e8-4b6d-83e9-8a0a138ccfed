"""
Views for the Course Service.

This module defines the API endpoints for the Course Service.

English: This file defines the API endpoints for the Course Service
Tanglish: Indha file Course Service-kku API endpoints-a define pannum
"""

from flask import Blueprint, jsonify
from course_service.controllers import (
    create_course, get_courses, get_course,
    map_student_to_course, get_student_courses
)

# Create a Blueprint for course routes
course_bp = Blueprint('course', __name__)

# Add route handlers for OPTIONS requests
@course_bp.route('/<path:path>', methods=['OPTIONS'])
def handle_options_path(path):
    """
    Handle OPTIONS requests for CORS preflight with path.

    Returns:
        Empty response with appropriate CORS headers

    English: This endpoint handles OPTIONS requests for CORS
    Tanglish: Indha endpoint CORS-kku OPTIONS requests-a handle pannum
    """
    return "", 200

@course_bp.route('/', methods=['OPTIONS'])
def handle_options_root():
    """
    Handle OPTIONS requests for CORS preflight at root.

    Returns:
        Empty response with appropriate CORS headers

    English: This endpoint handles OPTIONS requests for CORS at root
    Tanglish: Indha endpoint CORS-kku OPTIONS requests-a root-la handle pannum
    """
    return "", 200

@course_bp.route('/courses', methods=['POST'])
def create_course_route():
    """
    Create course endpoint.

    Returns:
        Response from create_course controller

    English: This endpoint creates a new course
    Tanglish: Indha endpoint puthusa oru course-a create pannum
    """
    return create_course()

@course_bp.route('/courses', methods=['GET'])
def get_courses_route():
    """
    Get all courses endpoint.

    Returns:
        Response from get_courses controller

    English: This endpoint gets all courses
    Tanglish: Indha endpoint ella courses-um get pannum
    """
    return get_courses()

@course_bp.route('/courses/<int:course_id>', methods=['GET'])
def get_course_route(course_id):
    """
    Get a specific course endpoint.

    Args:
        course_id: ID of the course to get

    Returns:
        Response from get_course controller

    English: This endpoint gets a specific course
    Tanglish: Indha endpoint specific course-a get pannum
    """
    return get_course(course_id)

@course_bp.route('/map-student', methods=['POST'])
def map_student_route():
    """
    Map student to course endpoint.

    Returns:
        Response from map_student_to_course controller

    English: This endpoint maps a student to a course
    Tanglish: Indha endpoint oru student-a oru course-oda map pannum
    """
    return map_student_to_course()

@course_bp.route('/student-courses/<int:student_id>', methods=['GET'])
def get_student_courses_route(student_id):
    """
    Get all courses for a specific student endpoint.

    Args:
        student_id: ID of the student

    Returns:
        Response from get_student_courses controller

    English: This endpoint gets all courses for a specific student
    Tanglish: Indha endpoint specific student-kku ella courses-um get pannum
    """
    return get_student_courses(student_id)

@course_bp.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint.

    Returns:
        JSON response indicating the service is running

    English: This endpoint checks if the service is running
    Tanglish: Indha endpoint service odi kondu irukka nu check pannum
    """
    return jsonify({"status": "healthy", "service": "course"})
