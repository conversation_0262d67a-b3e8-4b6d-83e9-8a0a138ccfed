"""
Helper script to run the Auth Service.

This script adds the project root to the Python path and then runs the Auth Service.
"""

import os
import sys

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

print("Starting Auth Service...")
print(f"Project root: {project_root}")
print(f"Python path: {sys.path}")

try:
    # Now import and run the Auth Service
    from auth_service.app import create_app

    if __name__ == '__main__':
        # Get port from environment variable or use default
        port = int(os.environ.get('AUTH_SERVICE_PORT', 5000))
        print(f"Auth Service will run on port {port}")

        # Create and run the app
        app = create_app()
        app.run(host='0.0.0.0', port=5000)
except Exception as e:
    print(f"Error starting Auth Service: {e}")
    import traceback
    traceback.print_exc()
