/**
 * Input Examples Component
 * 
 * This component demonstrates how to use the enhanced Input component
 * with different user roles (super_admin, admin, teacher, student, parent).
 */

import React, { useState } from 'react';
import Input from '../field/Input';

const InputExamples = () => {
  // Example state for super_admin
  const [superAdminForm, setSuperAdminForm] = useState({
    username: '',
    password: '',
    email: ''
  });

  // Example state for admin
  const [adminForm, setAdminForm] = useState({
    username: '',
    password: '',
    email: ''
  });

  // Example state for teacher
  const [teacherForm, setTeacherForm] = useState({
    username: '',
    password: '',
    email: '',
    course: ''
  });

  // Example state for student
  const [studentForm, setStudentForm] = useState({
    username: '',
    password: '',
    email: '',
    first_name: '',
    last_name: '',
    date_of_birth: '',
    address: '',
    phone: ''
  });

  // Example state for parent
  const [parentForm, setParentForm] = useState({
    username: '',
    password: '',
    email: '',
    first_name: '',
    last_name: '',
    occupation: '',
    address: '',
    phone: ''
  });

  // Handle input changes for each form
  const handleSuperAdminChange = (e) => {
    const { name, value } = e.target;
    setSuperAdminForm({
      ...superAdminForm,
      [name]: value
    });
  };

  const handleAdminChange = (e) => {
    const { name, value } = e.target;
    setAdminForm({
      ...adminForm,
      [name]: value
    });
  };

  const handleTeacherChange = (e) => {
    const { name, value } = e.target;
    setTeacherForm({
      ...teacherForm,
      [name]: value
    });
  };

  const handleStudentChange = (e) => {
    const { name, value } = e.target;
    setStudentForm({
      ...studentForm,
      [name]: value
    });
  };

  const handleParentChange = (e) => {
    const { name, value } = e.target;
    setParentForm({
      ...parentForm,
      [name]: value
    });
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Input Component Examples</h1>

      {/* Super Admin Inputs */}
      <div className="mb-8 p-4 border rounded-lg bg-purple-50">
        <h2 className="text-xl font-semibold mb-4 text-purple-800">Super Admin Inputs</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            id="super-admin-username"
            name="username"
            label="Username"
            value={superAdminForm.username}
            onChange={handleSuperAdminChange}
            required
            userRole="super_admin"
          />
          <Input
            id="super-admin-password"
            name="password"
            type="password"
            label="Password"
            value={superAdminForm.password}
            onChange={handleSuperAdminChange}
            required
            userRole="super_admin"
          />
          <Input
            id="super-admin-email"
            name="email"
            type="email"
            label="Email"
            value={superAdminForm.email}
            onChange={handleSuperAdminChange}
            required
            userRole="super_admin"
          />
        </div>
      </div>

      {/* Admin Inputs */}
      <div className="mb-8 p-4 border rounded-lg bg-green-50">
        <h2 className="text-xl font-semibold mb-4 text-green-800">Admin Inputs</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            id="admin-username"
            name="username"
            label="Username"
            value={adminForm.username}
            onChange={handleAdminChange}
            required
            userRole="admin"
          />
          <Input
            id="admin-password"
            name="password"
            type="password"
            label="Password"
            value={adminForm.password}
            onChange={handleAdminChange}
            required
            userRole="admin"
          />
          <Input
            id="admin-email"
            name="email"
            type="email"
            label="Email"
            value={adminForm.email}
            onChange={handleAdminChange}
            required
            userRole="admin"
          />
        </div>
      </div>

      {/* Teacher Inputs */}
      <div className="mb-8 p-4 border rounded-lg bg-indigo-50">
        <h2 className="text-xl font-semibold mb-4 text-indigo-800">Teacher Inputs</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            id="teacher-username"
            name="username"
            label="Username"
            value={teacherForm.username}
            onChange={handleTeacherChange}
            required
            userRole="teacher"
          />
          <Input
            id="teacher-password"
            name="password"
            type="password"
            label="Password"
            value={teacherForm.password}
            onChange={handleTeacherChange}
            required
            userRole="teacher"
          />
          <Input
            id="teacher-email"
            name="email"
            type="email"
            label="Email"
            value={teacherForm.email}
            onChange={handleTeacherChange}
            required
            userRole="teacher"
          />
          <Input
            id="teacher-course"
            name="course"
            label="Course"
            value={teacherForm.course}
            onChange={handleTeacherChange}
            required
            userRole="teacher"
          />
        </div>
      </div>

      {/* Student Inputs */}
      <div className="mb-8 p-4 border rounded-lg bg-blue-50">
        <h2 className="text-xl font-semibold mb-4 text-blue-800">Student Inputs</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            id="student-username"
            name="username"
            label="Username"
            value={studentForm.username}
            onChange={handleStudentChange}
            required
            userRole="student"
          />
          <Input
            id="student-password"
            name="password"
            type="password"
            label="Password"
            value={studentForm.password}
            onChange={handleStudentChange}
            required
            userRole="student"
          />
          <Input
            id="student-email"
            name="email"
            type="email"
            label="Email"
            value={studentForm.email}
            onChange={handleStudentChange}
            required
            userRole="student"
          />
          <Input
            id="student-first-name"
            name="first_name"
            label="First Name"
            value={studentForm.first_name}
            onChange={handleStudentChange}
            required
            userRole="student"
          />
          <Input
            id="student-last-name"
            name="last_name"
            label="Last Name"
            value={studentForm.last_name}
            onChange={handleStudentChange}
            required
            userRole="student"
          />
          <Input
            id="student-dob"
            name="date_of_birth"
            type="date"
            label="Date of Birth"
            value={studentForm.date_of_birth}
            onChange={handleStudentChange}
            userRole="student"
          />
          <Input
            id="student-address"
            name="address"
            label="Address"
            value={studentForm.address}
            onChange={handleStudentChange}
            userRole="student"
          />
          <Input
            id="student-phone"
            name="phone"
            label="Phone"
            value={studentForm.phone}
            onChange={handleStudentChange}
            userRole="student"
          />
        </div>
      </div>

      {/* Parent Inputs */}
      <div className="mb-8 p-4 border rounded-lg bg-teal-50">
        <h2 className="text-xl font-semibold mb-4 text-teal-800">Parent Inputs</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            id="parent-username"
            name="username"
            label="Username"
            value={parentForm.username}
            onChange={handleParentChange}
            required
            userRole="parent"
          />
          <Input
            id="parent-password"
            name="password"
            type="password"
            label="Password"
            value={parentForm.password}
            onChange={handleParentChange}
            required
            userRole="parent"
          />
          <Input
            id="parent-email"
            name="email"
            type="email"
            label="Email"
            value={parentForm.email}
            onChange={handleParentChange}
            required
            userRole="parent"
          />
          <Input
            id="parent-first-name"
            name="first_name"
            label="First Name"
            value={parentForm.first_name}
            onChange={handleParentChange}
            required
            userRole="parent"
          />
          <Input
            id="parent-last-name"
            name="last_name"
            label="Last Name"
            value={parentForm.last_name}
            onChange={handleParentChange}
            required
            userRole="parent"
          />
          <Input
            id="parent-occupation"
            name="occupation"
            label="Occupation"
            value={parentForm.occupation}
            onChange={handleParentChange}
            userRole="parent"
          />
          <Input
            id="parent-address"
            name="address"
            label="Address"
            value={parentForm.address}
            onChange={handleParentChange}
            userRole="parent"
          />
          <Input
            id="parent-phone"
            name="phone"
            label="Phone"
            value={parentForm.phone}
            onChange={handleParentChange}
            userRole="parent"
          />
        </div>
      </div>
    </div>
  );
};

export default InputExamples;
