"""
Script to test student registration from teacher dashboard.
"""

import requests
import json
import random
import string

def test_student_registration():
    """Test student registration with teacher's main_code."""
    print("Testing student registration from teacher dashboard...")

    # Step 1: Login as teacher to get token and main_code
    login_url = "http://localhost:5000/api/auth/login"
    login_data = {
        "username": "teacher1",
        "password": "password123"
    }

    try:
        login_response = requests.post(login_url, json=login_data)

        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.text}")
            return False

        login_result = login_response.json()
        token = login_result.get('token')
        user_data = login_result.get('user', {})
        main_code = user_data.get('main_code')

        print(f"✅ Teacher logged in successfully")
        print(f"Teacher main_code: {main_code}")

        if not main_code:
            print(f"❌ No main_code found for teacher!")
            return False

        # Step 2: Register a new user (student)
        print(f"\n--- Registering new student user ---")
        user_register_url = "http://localhost:5001/api/users/register"
        user_data_to_register = {
            "username": "student_test1",
            "password": "password123",
            "email": "<EMAIL>",
            "role": "Student",
            "main_code": main_code  # Use teacher's main_code
        }

        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }

        user_register_response = requests.post(user_register_url, json=user_data_to_register, headers=headers)
        print(f"User registration status: {user_register_response.status_code}")

        if user_register_response.status_code == 201:
            user_result = user_register_response.json()
            new_user = user_result.get('user', {})
            new_user_id = new_user.get('id')
            new_user_main_code = new_user.get('main_code')

            print(f"✅ User registered successfully")
            print(f"New user ID: {new_user_id}")
            print(f"New user main_code: {new_user_main_code}")

            if new_user_main_code == main_code:
                print(f"✅ User main_code matches teacher's main_code!")
            else:
                print(f"❌ User main_code doesn't match teacher's main_code!")

            # Step 3: Register student profile
            print(f"\n--- Registering student profile ---")
            student_register_url = "http://localhost:5002/api/students/students"
            student_data = {
                "user_id": new_user_id,
                "first_name": "Test",
                "last_name": "Student",
                "date_of_birth": "2000-01-01",
                "address": "123 Test St",
                "phone": "1234567890",
                "main_code": main_code  # Use teacher's main_code
            }

            student_register_response = requests.post(student_register_url, json=student_data, headers=headers)
            print(f"Student registration status: {student_register_response.status_code}")

            if student_register_response.status_code == 201:
                print(f"✅ Student profile registered successfully!")
                print(f"Response: {student_register_response.text}")
                return True
            else:
                print(f"❌ Student registration failed: {student_register_response.text}")
                return False

        else:
            print(f"❌ User registration failed: {user_register_response.text}")
            return False

    except Exception as e:
        print(f"Error during test: {str(e)}")
        return False

if __name__ == '__main__':
    test_student_registration()
