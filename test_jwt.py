"""
Test JWT token generation and verification.

This script tests the JWT token generation and verification functionality.
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the project root to the Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

# Import the JWT utility functions
from jwt_utils import generate_token, verify_token, get_jwt_secret

def test_jwt_utils():
    """
    Test the JWT utility functions.
    """
    print("Testing JWT utility functions...")
    
    # Test JWT secret
    jwt_secret = get_jwt_secret()
    print(f"JWT secret: {jwt_secret}")
    
    # Test token generation
    user_data = {
        'id': 1,
        'username': 'testuser',
        'role': 'Admin',
        'is_admin': True
    }
    
    token = generate_token(user_data)
    print(f"Generated token: {token}")
    
    # Test token verification
    payload = verify_token(token)
    print(f"Verified token payload: {payload}")
    
    if payload:
        print("Token verification successful!")
    else:
        print("Token verification failed!")

def test_auth_service():
    """
    Test the Auth Service login and token verification.
    """
    print("\nTesting Auth Service login and token verification...")
    
    # Login as Super Admin
    login_url = "http://localhost:5000/api/auth/login"
    login_data = {
        "username": "superadmin",
        "password": "password123"
    }
    
    try:
        login_response = requests.post(login_url, json=login_data)
        print(f"Login response status: {login_response.status_code}")
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get("token")
            print(f"Login successful! Token: {token}")
            
            # Verify token
            verify_url = "http://localhost:5000/api/auth/verify"
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            verify_response = requests.get(verify_url, headers=headers)
            print(f"Verify response status: {verify_response.status_code}")
            print(f"Verify response: {verify_response.text}")
            
            if verify_response.status_code == 200:
                print("Token verification successful!")
            else:
                print("Token verification failed!")
        else:
            print(f"Login failed! Response: {login_response.text}")
    except Exception as e:
        print(f"Error testing Auth Service: {str(e)}")

def test_user_service():
    """
    Test the User Service with the token from Auth Service.
    """
    print("\nTesting User Service with token from Auth Service...")
    
    # Login as Super Admin
    login_url = "http://localhost:5000/api/auth/login"
    login_data = {
        "username": "superadmin",
        "password": "password123"
    }
    
    try:
        login_response = requests.post(login_url, json=login_data)
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            token = login_result.get("token")
            print(f"Login successful! Token: {token}")
            
            # Register a new teacher
            register_url = "http://localhost:5001/api/users/register"
            register_data = {
                "username": "teacher_test",
                "password": "password123",
                "email": "<EMAIL>",
                "role": "Teacher"
            }
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }
            
            register_response = requests.post(register_url, json=register_data, headers=headers)
            print(f"Register response status: {register_response.status_code}")
            print(f"Register response: {register_response.text}")
            
            if register_response.status_code == 201:
                print("Teacher registration successful!")
            else:
                print("Teacher registration failed!")
        else:
            print(f"Login failed! Response: {login_response.text}")
    except Exception as e:
        print(f"Error testing User Service: {str(e)}")

if __name__ == "__main__":
    # Test JWT utility functions
    test_jwt_utils()
    
    # Test Auth Service
    test_auth_service()
    
    # Test User Service
    test_user_service()
